# RSS Aggregator - Historia Zmian

## [1.2.0] - 2024-01-20

### ✨ Nowe Funkcje
- **Przypisywanie do użytkowników**: Kanały RSS mogą być teraz przypisane do konkretnych użytkowników WordPress
- **Podwójne przypisywanie**: Mo<PERSON><PERSON><PERSON><PERSON><PERSON> jednoczesnego przypisania kanału do miejsca GeoDirectory i użytkownika
- **Inteligentne pola tekstowe**: Zastąpiono selecty polami tekstowymi z autocomplete
  - Podpowiedzi miejsc GeoDirectory podczas wpisywania
  - Podpowiedzi użytkowników WordPress podczas wpisywania
  - Responsywne sugestie z dodatkowymi informacjami
- **Zaawansowane pobieranie miniaturek**: Nowy system pobierania miniaturek ze stron internetowych
  - Automatyczne pobieranie og:image z meta tagów
  - Wsparcie dla Twitter Card images
  - Pobieranie z Schema.org meta tagów
  - Fallback do pierwszego obrazu w treści artykułu
  - Inteligentne pomijanie ikon, awatarów i reklam

### 🔧 Ulepszenia
- Dodano kolumnę "Assignments" w liście kanałów RSS
- Nowe pola w bazie danych: `geodirectory_place_name`, `assigned_user_id`, `assigned_user_name`
- Ulepszone style CSS dla autocomplete i przypisań
- Dodano AJAX handlery dla wyszukiwania miejsc i użytkowników
- Automatyczna migracja bazy danych dla istniejących instalacji

### 🎨 Interface
- Nowa kolumna "Assignments" z wizualnymi wskaźnikami
- Ikony dla miejsc (📍) i użytkowników (👤)
- Kolorowe badges dla różnych typów przypisań
- Responsywne sugestie autocomplete z detalami

### 🛠️ Techniczne
- Nowa klasa `RSS_Thumbnail_Extractor` do zaawansowanego pobierania miniaturek
- Rozszerzone metody w `RSS_Database` dla wyszukiwania użytkowników i miejsc
- Dodano indeksy bazy danych dla lepszej wydajności
- Zaktualizowano wersję bazy danych do 1.2.0

---

## [1.1.0] - 2024-01-15

### ✨ Nowe Funkcje
- **Kontrola Pierwszego Importu**: Dodano możliwość wyboru liczby wpisów pobieranych przy pierwszym uruchomieniu kanału RSS
  - Nowe pole "Initial Import Count" w formularzu kanału (zakres: 1-100)
  - Domyślne ustawienie globalne w Settings → General
  - Wpisy sortowane od najnowszego przy pierwszym imporcie
  - Kolejne aktualizacje pobierają wszystkie nowe wpisy normalnie

### 🔧 Ulepszenia
- Dodano kolumnę "Initial Count" w liście kanałów RSS
- Dodano wskaźnik czy kanał był już importowany
- Ulepszone sortowanie wpisów RSS według daty publikacji
- Dodano walidację zakresu 1-100 dla liczby importu
- Dodano migrację bazy danych dla istniejących instalacji

### 📚 Dokumentacja
- Zaktualizowano INSTALLATION.md z informacjami o nowej funkcji
- Dodano scenariusze testowe w TESTING.md
- Rozszerzono EXAMPLES.md o przykłady użycia
- Dodano tłumaczenia dla nowych elementów interfejsu

### 🛠️ Techniczne
- Dodano kolumnę `initial_import_count` do tabeli `wp_rss_aggregator_feeds`
- Zmodyfikowano metodę `update_single_feed()` z obsługą pierwszego importu
- Dodano metodę `sort_items_by_date()` dla sortowania wpisów
- Dodano automatyczną migrację bazy danych
- Zaktualizowano wersję bazy danych do 1.1.0

---

## [1.0.0] - 2024-01-01

### 🎉 Pierwsze Wydanie
- **Podstawowe Funkcje RSS**:
  - Dodawanie i zarządzanie kanałami RSS
  - Automatyczne pobieranie i parsowanie RSS 2.0, Atom, RDF
  - Tworzenie postów WordPress z metadanymi
  - Pobieranie miniaturek z kanałów i stron

- **Panel Administracyjny**:
  - Lista kanałów z akcjami (edycja, test, aktualizacja, usuwanie)
  - Formularz dodawania/edycji kanałów z walidacją
  - Testowanie kanałów RSS w czasie rzeczywistym
  - Strona ustawień z zakładkami
  - Strona statusu z diagnostyką systemu

- **Integracje**:
  - **BuddyBoss**: Publikacja w strumieniu aktywności
  - **GeoDirectory**: Przypisywanie do miejsc i powiatów
  - **WP-Cron**: Automatyczne aktualizacje z konfigurowalnymi częstotliwościami

- **Frontend**:
  - Shortcody: `[rss_aggregator_feed]`, `[rss_aggregator_county]`, `[rss_aggregator_recent]`
  - Informacje o źródle w postach
  - Responsywne style CSS
  - Śledzenie analityczne

- **Zarządzanie**:
  - Automatyczne czyszczenie starych wpisów
  - Cache'owanie kanałów RSS
  - Obsługa błędów i logowanie
  - Kompletna dezinstalacja

### 📋 Wymagania Systemowe
- WordPress 5.0+
- PHP 7.4+
- MySQL 5.6+ / MariaDB 10.1+
- Rozszerzenia PHP: curl, xml, dom, mbstring

### 🔧 Konfiguracja
- Automatyczne tworzenie tabel bazy danych
- Domyślne ustawienia wtyczki
- Planowanie zadań cron
- Integracja z istniejącymi wtyczkami

---

## Planowane Funkcje

### [1.2.0] - Planowane
- **Filtry i Kategorie**:
  - Filtrowanie wpisów według słów kluczowych
  - Automatyczne przypisywanie kategorii
  - Wykluczanie określonych treści

- **Zaawansowane Integracje**:
  - Integracja z Elementor
  - Widget dla sidebar
  - REST API endpoints

### [1.3.0] - Planowane
- **Analityka i Raporty**:
  - Statystyki popularności kanałów
  - Raporty aktywności RSS
  - Dashboard z wykresami

- **Import/Export**:
  - Eksport ustawień kanałów
  - Import z OPML
  - Backup i przywracanie

### [1.4.0] - Planowane
- **Zaawansowane Funkcje**:
  - Pełnotekstowe wyszukiwanie
  - Duplikaty i podobne artykuły
  - Automatyczne tagi

---

## Wsparcie i Zgłaszanie Błędów

### 🐛 Znane Problemy
- Brak znanych problemów w wersji 1.1.0

### 📞 Wsparcie
- Sprawdź dokumentację w plikach README.md, INSTALLATION.md, TESTING.md
- Użyj strony Status w panelu administracyjnym do diagnostyki
- Sprawdź logi błędów WordPress

### 🔄 Aktualizacje
- Wtyczka automatycznie sprawdza dostępność aktualizacji
- Backup bazy danych przed aktualizacją jest zalecany
- Migracje bazy danych uruchamiają się automatycznie

---

## Licencja
GPL v2 lub nowsza

## Autorzy
- Główny deweloper: RSS Aggregator Team
- Integracje: BuddyBoss & GeoDirectory specialists

## Podziękowania
- Społeczność WordPress za wsparcie
- Testerzy beta za feedback
- Twórcy BuddyBoss i GeoDirectory za dokumentację API
