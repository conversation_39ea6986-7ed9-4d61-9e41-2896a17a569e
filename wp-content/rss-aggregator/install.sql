-- RSS Aggregator Pro - Complete Database Schema
-- Execute these commands in phpMyAdmin or your database management tool
-- Replace 'wp_' with your actual WordPress table prefix

-- =====================================================
-- 1. CREATE FEEDS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS `wp_rss_aggregator_feeds` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `url` text NOT NULL,
  `geodirectory_place_id` bigint(20) DEFAULT NULL,
  `geodirectory_place_name` varchar(255) DEFAULT NULL,
  `assigned_user_id` bigint(20) DEFAULT NULL,
  `assigned_user_name` varchar(255) DEFAULT NULL,
  `county` varchar(255) DEFAULT NULL,
  `region` varchar(255) DEFAULT NULL,
  `update_frequency` varchar(50) DEFAULT 'hourly',
  `initial_import_count` int(11) DEFAULT 10,
  `status` varchar(20) DEFAULT 'active',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `last_updated` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_update_frequency` (`update_frequency`),
  KEY `idx_geodirectory_place` (`geodirectory_place_id`),
  KEY `idx_assigned_user` (`assigned_user_id`),
  KEY `idx_county` (`county`),
  KEY `idx_region` (`region`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 2. CREATE ITEMS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS `wp_rss_aggregator_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `feed_id` int(11) NOT NULL,
  `post_id` bigint(20) DEFAULT NULL,
  `title` text NOT NULL,
  `description` longtext,
  `content` longtext,
  `url` text NOT NULL,
  `guid` varchar(255) DEFAULT NULL,
  `pub_date` datetime DEFAULT NULL,
  `thumbnail_url` text DEFAULT NULL,
  `processed` tinyint(1) DEFAULT 0,
  `author` varchar(255) DEFAULT NULL,
  `categories` text DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_feed_id` (`feed_id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_processed` (`processed`),
  KEY `idx_pub_date` (`pub_date`),
  KEY `idx_guid` (`guid`),
  FOREIGN KEY (`feed_id`) REFERENCES `wp_rss_aggregator_feeds` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 3. INSERT DEFAULT SETTINGS
-- =====================================================

-- Default plugin settings (stored in wp_options)
INSERT IGNORE INTO `wp_options` (`option_name`, `option_value`, `autoload`) VALUES
('rss_aggregator_post_author', '1', 'yes'),
('rss_aggregator_post_status', 'publish', 'yes'),
('rss_aggregator_enable_thumbnails', '1', 'yes'),
('rss_aggregator_default_frequency', 'hourly', 'yes'),
('rss_aggregator_max_items_per_feed', '50', 'yes'),
('rss_aggregator_cleanup_old_items', '1', 'yes'),
('rss_aggregator_cleanup_days', '30', 'yes'),
('rss_aggregator_version', '2.0.0', 'yes');

-- =====================================================
-- 4. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Additional indexes for better performance
CREATE INDEX IF NOT EXISTS `idx_feeds_created_at` ON `wp_rss_aggregator_feeds` (`created_at`);
CREATE INDEX IF NOT EXISTS `idx_feeds_last_updated` ON `wp_rss_aggregator_feeds` (`last_updated`);
CREATE INDEX IF NOT EXISTS `idx_items_created_at` ON `wp_rss_aggregator_items` (`created_at`);
CREATE INDEX IF NOT EXISTS `idx_items_feed_processed` ON `wp_rss_aggregator_items` (`feed_id`, `processed`);

-- =====================================================
-- 5. SAMPLE DATA (OPTIONAL)
-- =====================================================

-- Uncomment the following lines to insert sample feed data
/*
INSERT INTO `wp_rss_aggregator_feeds` 
(`name`, `url`, `update_frequency`, `initial_import_count`, `status`) 
VALUES 
('WordPress News', 'https://wordpress.org/news/feed/', 'hourly', 10, 'active'),
('TechCrunch', 'https://techcrunch.com/feed/', 'hourly', 10, 'active');
*/

-- =====================================================
-- INSTALLATION COMPLETE
-- =====================================================

-- Verify installation by running:
-- SELECT COUNT(*) FROM wp_rss_aggregator_feeds;
-- SELECT COUNT(*) FROM wp_rss_aggregator_items;
-- SELECT option_value FROM wp_options WHERE option_name = 'rss_aggregator_version';
