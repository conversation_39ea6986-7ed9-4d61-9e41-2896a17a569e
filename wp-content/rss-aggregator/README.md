# RSS Aggregator Pro

Advanced RSS feed aggregator for WordPress with GeoDirectory and BuddyBoss integration.

## Features

### 🚀 Core Features
- **RSS Feed Management** - Add, edit, delete, and manage multiple RSS feeds
- **Automatic Content Import** - Scheduled imports with configurable frequency
- **Thumbnail Extraction** - Automatic thumbnail extraction from RSS feeds and web pages
- **Place-Based Publishing** - Assign feeds to GeoDirectory places
- **User Assignment** - Assign feeds to specific WordPress users
- **Geographic Filtering** - County and region-based feed organization

### 🔗 Integrations
- **GeoDirectory** - Full integration with places and locations
- **BuddyBoss** - Activity stream integration
- **WordPress Cron** - Automated feed updates
- **WordPress Media Library** - Automatic thumbnail management

### 📊 Management Features
- **Feed Statistics** - Detailed analytics for each feed
- **Post Management** - Individual post deletion and management
- **Debug Tools** - Comprehensive debugging and troubleshooting
- **Bulk Operations** - Mass feed management capabilities

## Installation

### 1. Database Setup
Execute the SQL file to create required tables:
```sql
-- Import the install.sql file in phpMyAdmin
-- Replace 'wp_' with your actual WordPress table prefix
```

### 2. Plugin Installation
1. Upload the plugin folder to `/wp-content/plugins/`
2. Activate the plugin through WordPress admin
3. Configure settings in **RSS Aggregator → Settings**

### 3. Configuration
1. Set default post author in Settings
2. Configure thumbnail extraction options
3. Set up cron schedules for automatic updates

## Usage

### Adding RSS Feeds
1. Go to **RSS Aggregator → Add New**
2. Fill in feed details:
   - **Name** - Display name for the feed
   - **URL** - RSS feed URL
   - **GeoDirectory Place** - Optional place assignment
   - **Assigned User** - Optional user assignment
   - **County/Region** - Geographic classification
   - **Update Frequency** - How often to check for updates
   - **Initial Import Count** - Number of posts to import initially

### Author Priority System
The plugin determines post authors based on priority:
1. **Place Owner** - If GeoDirectory place is assigned, uses place owner
2. **Assigned User** - If user is assigned to feed, uses that user
3. **Default Author** - Uses default author from plugin settings

### Feed Statistics
Access detailed statistics for each feed:
- Total items imported
- Success rate
- Last update time
- Recent activity
- Post management tools

## Technical Details

### Database Tables
- `wp_rss_aggregator_feeds` - Feed configuration and metadata
- `wp_rss_aggregator_items` - Individual RSS items and processing status

### Cron Jobs
- Automatic feed updates based on configured frequency
- Cleanup of old items (configurable retention period)

### Thumbnail Extraction
The plugin extracts thumbnails from:
1. RSS feed enclosures
2. RSS feed media elements
3. Web page og:image tags
4. Web page twitter:image tags
5. First content image in articles

## Requirements

- **WordPress** 5.0 or higher
- **PHP** 7.4 or higher
- **MySQL** 5.7 or higher
- **GeoDirectory** (optional, for place integration)
- **BuddyBoss** (optional, for activity integration)

## Changelog

### Version 2.0.0
- Complete rewrite with improved architecture
- Added place-based publishing system
- Enhanced thumbnail extraction
- Improved user interface
- Added comprehensive statistics
- Better error handling and debugging
- Full GeoDirectory and BuddyBoss integration

### Version 1.3.0
- Initial release with basic RSS aggregation
- Simple feed management
- Basic thumbnail support

## Support

For support and bug reports, please contact:
- **Author**: Łukasz Nowak
- **Website**: https://foto-nowak.pl
- **GitHub**: https://github.com/luknow86/rss-aggregator-pro

## License

This plugin is licensed under the GPL v2 or later.

## Credits

Developed by Łukasz Nowak for advanced RSS content management with WordPress, GeoDirectory, and BuddyBoss integration.


