<?php
/**
 * Debug Feed Structure and Author Assignment
 * 
 * Access via: /wp-content/plugins/rss-aggregator/debug-feed-structure.php
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. You must be an administrator to run this script.');
}

echo "<h1>RSS Aggregator Feed Structure Debug</h1>";

global $wpdb;

// Check table structure
echo "<h2>1. Table Structure</h2>";
$table_name = $wpdb->prefix . 'rss_aggregator_feeds';
$columns = $wpdb->get_results("SHOW COLUMNS FROM {$table_name}");

echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
foreach ($columns as $column) {
    echo "<tr>";
    echo "<td>{$column->Field}</td>";
    echo "<td>{$column->Type}</td>";
    echo "<td>{$column->Null}</td>";
    echo "<td>{$column->Key}</td>";
    echo "<td>{$column->Default}</td>";
    echo "<td>{$column->Extra}</td>";
    echo "</tr>";
}
echo "</table>";

// Check if required columns exist
$required_columns = ['assigned_user_id', 'assigned_user_name', 'geodirectory_place_id', 'geodirectory_place_name', 'region'];
$existing_columns = array_column($columns, 'Field');

echo "<h2>2. Required Columns Check</h2>";
foreach ($required_columns as $col) {
    $exists = in_array($col, $existing_columns);
    $status = $exists ? '✅ EXISTS' : '❌ MISSING';
    echo "<p><strong>{$col}:</strong> {$status}</p>";
}

// Get sample feeds
echo "<h2>3. Sample Feed Data</h2>";
$feeds = $wpdb->get_results("SELECT * FROM {$table_name} LIMIT 3");

if ($feeds) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr>";
    foreach ($feeds[0] as $key => $value) {
        echo "<th>{$key}</th>";
    }
    echo "</tr>";
    
    foreach ($feeds as $feed) {
        echo "<tr>";
        foreach ($feed as $key => $value) {
            echo "<td>" . ($value ?? 'NULL') . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No feeds found in database.</p>";
}

// Test author determination
echo "<h2>4. Author Determination Test</h2>";

if ($feeds) {
    foreach ($feeds as $feed) {
        echo "<h3>Feed: {$feed->name} (ID: {$feed->id})</h3>";
        echo "<p><strong>geodirectory_place_id:</strong> " . ($feed->geodirectory_place_id ?? 'NULL') . "</p>";
        echo "<p><strong>assigned_user_id:</strong> " . ($feed->assigned_user_id ?? 'NULL') . "</p>";
        
        // Test place author
        if (!empty($feed->geodirectory_place_id)) {
            $place_post = get_post($feed->geodirectory_place_id);
            if ($place_post) {
                echo "<p><strong>Place post author:</strong> {$place_post->post_author}</p>";
                $place_user = get_user_by('id', $place_post->post_author);
                echo "<p><strong>Place author name:</strong> " . ($place_user ? $place_user->display_name : 'User not found') . "</p>";
            } else {
                echo "<p><strong>Place post:</strong> Not found</p>";
            }
        }
        
        // Test assigned user
        if (!empty($feed->assigned_user_id)) {
            $assigned_user = get_user_by('id', $feed->assigned_user_id);
            echo "<p><strong>Assigned user:</strong> " . ($assigned_user ? $assigned_user->display_name : 'User not found') . "</p>";
        }
        
        // Test default author
        $default_author = get_option('rss_aggregator_post_author', 1);
        $default_user = get_user_by('id', $default_author);
        echo "<p><strong>Default author (settings):</strong> {$default_author} - " . ($default_user ? $default_user->display_name : 'User not found') . "</p>";
        
        echo "<hr>";
    }
}

// Check recent posts and their authors
echo "<h2>5. Recent RSS Posts and Authors</h2>";
$recent_posts = $wpdb->get_results("
    SELECT p.ID, p.post_title, p.post_author, p.post_date, pm.meta_value as feed_id
    FROM {$wpdb->posts} p
    LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_rss_aggregator_feed_id'
    WHERE pm.meta_value IS NOT NULL
    ORDER BY p.post_date DESC
    LIMIT 10
");

if ($recent_posts) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Post ID</th><th>Title</th><th>Author ID</th><th>Author Name</th><th>Feed ID</th><th>Date</th></tr>";
    
    foreach ($recent_posts as $post) {
        $author = get_user_by('id', $post->post_author);
        echo "<tr>";
        echo "<td>{$post->ID}</td>";
        echo "<td>" . substr($post->post_title, 0, 50) . "...</td>";
        echo "<td>{$post->post_author}</td>";
        echo "<td>" . ($author ? $author->display_name : 'Unknown') . "</td>";
        echo "<td>{$post->feed_id}</td>";
        echo "<td>{$post->post_date}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No RSS posts found.</p>";
}

// Check users
echo "<h2>6. Available Users</h2>";
$users = get_users(array('number' => 10));
echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>ID</th><th>Login</th><th>Display Name</th><th>Email</th><th>Role</th></tr>";

foreach ($users as $user) {
    $roles = implode(', ', $user->roles);
    echo "<tr>";
    echo "<td>{$user->ID}</td>";
    echo "<td>{$user->user_login}</td>";
    echo "<td>{$user->display_name}</td>";
    echo "<td>{$user->user_email}</td>";
    echo "<td>{$roles}</td>";
    echo "</tr>";
}
echo "</table>";

echo "<p><a href='" . admin_url('admin.php?page=rss-aggregator') . "'>Go to RSS Aggregator</a></p>";
?>
