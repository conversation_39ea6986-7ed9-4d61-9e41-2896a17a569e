/**
 * RSS Aggregator Admin Styles
 *
 * @package RSS_Aggregator
 * @since 1.0.0
 */

/* Statistics Cards */
.rss-aggregator-stats {
    display: flex;
    gap: 20px;
    margin: 20px 0;
    flex-wrap: wrap;
}

.rss-stat-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    min-width: 150px;
    flex: 1;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.rss-stat-number {
    font-size: 32px;
    font-weight: 600;
    color: #1d2327;
    line-height: 1;
}

.rss-stat-label {
    font-size: 13px;
    color: #646970;
    margin-top: 5px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Empty State */
.rss-aggregator-empty-state {
    text-align: center;
    padding: 60px 20px;
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    margin: 20px 0;
}

.rss-empty-icon {
    font-size: 48px;
    color: #c3c4c7;
    margin-bottom: 20px;
}

.rss-aggregator-empty-state h2 {
    color: #1d2327;
    margin-bottom: 10px;
}

.rss-aggregator-empty-state p {
    color: #646970;
    margin-bottom: 30px;
    font-size: 16px;
}

/* Table Enhancements */
.column-county .county-badge {
    background: #2271b1;
    color: #fff;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.column-county .no-county {
    color: #646970;
    font-style: italic;
}

.status-active {
    color: #00a32a;
    font-weight: 500;
}

.status-inactive {
    color: #d63638;
    font-weight: 500;
}

.never-updated {
    color: #646970;
    font-style: italic;
}

.initial-count-badge {
    display: inline-block;
    background: #f0f0f1;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
    color: #2c3338;
}

.initial-count-badge .not-imported {
    color: #d63638;
    font-weight: normal;
    display: block;
    margin-top: 2px;
}

.initial-count-badge .imported {
    color: #00a32a;
    font-weight: normal;
    display: block;
    margin-top: 2px;
}

/* Autocomplete Styles */
.autocomplete-suggestions {
    position: absolute;
    z-index: 1000;
    background: #fff;
    border: 1px solid #ddd;
    border-top: none;
    max-height: 200px;
    overflow-y: auto;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    display: none;
    width: 100%;
    box-sizing: border-box;
}

.autocomplete-suggestion {
    padding: 10px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f1;
    transition: background-color 0.2s;
}

.autocomplete-suggestion:hover,
.autocomplete-suggestion.selected {
    background-color: #f0f6fc;
}

.autocomplete-suggestion:last-child {
    border-bottom: none;
}

.autocomplete-suggestion .name {
    font-weight: 500;
    color: #2c3338;
}

.autocomplete-suggestion .details {
    font-size: 12px;
    color: #646970;
    margin-top: 2px;
}

.autocomplete-place .details::before {
    content: "📍 ";
}

.autocomplete-user .details::before {
    content: "👤 ";
}

/* Form field positioning for autocomplete */
.form-table td {
    position: relative;
}

.autocomplete-place,
.autocomplete-user {
    position: relative;
}

/* Assignments column styles */
.assignments-list {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.assignment-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 3px;
    background: #f0f0f1;
}

.assignment-item .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

.place-assignment {
    background: #e7f3ff;
    color: #0073aa;
}

.place-assignment .dashicons {
    color: #0073aa;
}

.user-assignment {
    background: #f0f6fc;
    color: #2c3338;
}

.user-assignment .dashicons {
    color: #646970;
}

.no-assignments {
    color: #646970;
    font-style: italic;
    font-size: 12px;
}

.assignment-text {
    font-weight: 500;
}

/* Modal Styles */
.rss-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.rss-modal-content {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.rss-modal-header {
    padding: 20px 20px 0;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.rss-modal-header h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
}

.rss-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.rss-modal-close:hover {
    color: #000;
}

.rss-modal-body {
    padding: 20px;
}

.rss-modal-footer {
    padding: 0 20px 20px;
    text-align: right;
}

.rss-modal-footer .button {
    margin-left: 10px;
}

/* Form Styles */
.rss-aggregator-form .required {
    color: #d63638;
}

.rss-sample-items {
    margin-top: 15px;
    padding: 15px;
    background: #f6f7f7;
    border-radius: 4px;
}

.rss-sample-items h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
}

.rss-sample-items ul {
    margin: 0;
    padding-left: 20px;
}

.rss-sample-items li {
    margin-bottom: 10px;
}

/* Settings Tabs */
.rss-settings-tabs {
    margin-top: 20px;
}

.nav-tab-wrapper {
    border-bottom: 1px solid #c3c4c7;
    margin-bottom: 20px;
}

.tab-content {
    display: none;
    background: #fff;
    padding: 20px;
    border: 1px solid #c3c4c7;
    border-top: none;
    border-radius: 0 0 4px 4px;
}

.tab-content.active {
    display: block;
}

.tab-content h2 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

/* Status Page */
.rss-status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.rss-status-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.rss-status-card h2 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
    font-size: 18px;
}

.rss-status-full-width {
    grid-column: 1 / -1;
}

.rss-status-table {
    width: 100%;
    border-collapse: collapse;
}

.rss-status-table td {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f1;
}

.rss-status-table td:first-child {
    font-weight: 500;
    width: 40%;
}

.status-good {
    color: #00a32a;
    font-weight: 500;
}

.status-warning {
    color: #dba617;
    font-weight: 500;
}

.status-error {
    color: #d63638;
    font-weight: 500;
}

.rss-status-actions {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #ddd;
}

.rss-status-actions .button {
    margin-right: 10px;
}

.rss-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.rss-stat-item {
    text-align: center;
    padding: 15px;
    background: #f6f7f7;
    border-radius: 4px;
}

.rss-stat-item .rss-stat-number {
    font-size: 24px;
    font-weight: 600;
    color: #1d2327;
    line-height: 1;
}

.rss-stat-item .rss-stat-label {
    font-size: 12px;
    color: #646970;
    margin-top: 5px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Responsive Design */
@media (max-width: 782px) {
    .rss-aggregator-stats {
        flex-direction: column;
    }
    
    .rss-stat-card {
        min-width: auto;
    }
    
    .rss-status-grid {
        grid-template-columns: 1fr;
    }
    
    .rss-modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .rss-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .rss-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .rss-modal-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .rss-modal-close {
        align-self: flex-end;
        margin-top: -30px;
    }
}

/* Loading States */
.rss-loading {
    opacity: 0.6;
    pointer-events: none;
}

.rss-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: rss-spin 1s linear infinite;
}

@keyframes rss-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notices */
.notice.inline {
    margin: 10px 0;
    padding: 8px 12px;
}

/* Button Enhancements */
.button.button-delete {
    background: #d63638;
    border-color: #d63638;
    color: #fff;
}

.button.button-delete:hover {
    background: #b32d2e;
    border-color: #b32d2e;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.margin-top {
    margin-top: 20px;
}

.margin-bottom {
    margin-bottom: 20px;
}

.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}
