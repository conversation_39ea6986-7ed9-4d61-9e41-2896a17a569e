# RSS Aggregator - Przewodnik Testowania

## Testowe Kanały RSS

### Polskie Portale Informacyjne
```
1. TVN24
   URL: https://tvn24.pl/rss
   Opis: Główne wiadomości TVN24

2. Onet Wiadomości
   URL: https://wiadomosci.onet.pl/rss
   Opis: Wiadomości z portalu Onet

3. Interia Wydarzenia
   URL: https://wydarzenia.interia.pl/feed
   Opis: Wydarzenia z portalu Interia

4. Gazeta.pl
   URL: https://rss.gazeta.pl/pub/rss/gazeta_pl_wiadomosci.xml
   Opis: Wiadomości z Gazeta.pl

5. Polsat News
   URL: https://www.polsatnews.pl/rss/swiat.xml
   Opis: Wiadomości ze świata - Polsat News
```

### Lokalne Portale (Przykłady)
```
1. Kraków
   URL: https://krakow.pl/rss
   Powiat: krakowski

2. Gdańsk
   URL: https://gdansk.pl/rss
   Powiat: gdański

3. Wrocław
   URL: https://wroclaw.pl/rss
   Powiat: wrocławski
```

## Lista Kontrolna Testowania

### 1. Instalacja i Aktywacja
- [ ] Wtyczka aktywuje się bez błędów
- [ ] Tabele bazy danych zostają utworzone
- [ ] Menu "RSS Aggregator" pojawia się w panelu admin
- [ ] Domyślne ustawienia są zapisane

### 2. Panel Administracyjny

#### Lista Kanałów
- [ ] Strona ładuje się poprawnie
- [ ] Wyświetlają się statystyki (0/0/0/0 na początku)
- [ ] Pokazuje się komunikat "No RSS feeds yet"
- [ ] Przycisk "Add Your First Feed" działa

#### Dodawanie Kanału
- [ ] Formularz ładuje się poprawnie
- [ ] Walidacja pól wymaganych działa
- [ ] Pole "Initial Import Count" ma domyślną wartość z ustawień
- [ ] Walidacja zakresu 1-100 dla "Initial Import Count" działa
- [ ] Funkcja "Test Feed" działa dla poprawnych URL
- [ ] Funkcja "Test Feed" pokazuje błędy dla niepoprawnych URL
- [ ] Auto-generowanie nazwy z URL działa
- [ ] Zapisywanie kanału działa
- [ ] Przekierowanie po zapisaniu działa

#### Edycja Kanału
- [ ] Formularz ładuje się z danymi kanału
- [ ] Aktualizacja kanału działa
- [ ] Wszystkie pola są poprawnie wypełnione

#### Ustawienia
- [ ] Wszystkie zakładki ładują się poprawnie
- [ ] Zapisywanie ustawień działa
- [ ] Walidacja wartości działa

#### Status
- [ ] Informacje systemowe wyświetlają się poprawnie
- [ ] Status WP-Cron jest poprawny
- [ ] Status integracji (BuddyBoss/GeoDirectory) jest poprawny
- [ ] Harmonogram cron wyświetla się poprawnie

### 3. Funkcjonalności RSS

#### Pobieranie Kanałów
- [ ] Ręczne "Update Now" działa
- [ ] Pobieranie treści RSS działa
- [ ] Parsowanie różnych formatów RSS (RSS 2.0, Atom, RDF)
- [ ] Pierwszy import ogranicza liczbę wpisów zgodnie z ustawieniem
- [ ] Wpisy są sortowane od najnowszego przy pierwszym imporcie
- [ ] Kolejne aktualizacje pobierają wszystkie nowe wpisy
- [ ] Tworzenie postów WordPress działa
- [ ] Metadane postów są poprawnie zapisane

#### Miniaturki
- [ ] Pobieranie miniaturek z kanału RSS
- [ ] Pobieranie miniaturek ze stron (og:image, twitter:image)
- [ ] Ustawianie featured image dla postów
- [ ] Obsługa błędów przy pobieraniu obrazków

#### WP-Cron
- [ ] Zadania cron są planowane
- [ ] Automatyczne aktualizacje działają
- [ ] Różne częstotliwości działają poprawnie
- [ ] Reset harmonogramów działa

### 4. Integracje

#### BuddyBoss (jeśli zainstalowany)
- [ ] Wpisy RSS pojawiają się w strumieniu aktywności
- [ ] Informacje o źródle są wyświetlane
- [ ] Miniaturki wyświetlają się w aktywnościach
- [ ] Linki do oryginalnych artykułów działają

#### GeoDirectory (jeśli zainstalowany)
- [ ] Lista miejsc ładuje się w formularzu
- [ ] Przypisywanie kanałów do miejsc działa
- [ ] Pola niestandardowe są tworzone
- [ ] Powiaty są poprawnie przypisywane

### 5. Frontend

#### Wyświetlanie Postów
- [ ] Posty RSS wyświetlają się na stronie głównej
- [ ] Informacje o źródle są dodawane do treści
- [ ] Linki do oryginalnych artykułów działają
- [ ] Style CSS są ładowane poprawnie

#### Shortcodes
- [ ] `[rss_aggregator_feed]` działa
- [ ] `[rss_aggregator_county]` działa
- [ ] `[rss_aggregator_recent]` działa
- [ ] Parametry shortcodów działają poprawnie

### 6. Wydajność i Bezpieczeństwo

#### Wydajność
- [ ] Cache RSS działa (15 minut)
- [ ] Strony ładują się szybko
- [ ] Brak problemów z pamięcią PHP
- [ ] Brak nadmiernych zapytań do bazy danych

#### Bezpieczeństwo
- [ ] Walidacja danych wejściowych
- [ ] Sanityzacja danych wyjściowych
- [ ] Nonce w formularzach AJAX
- [ ] Sprawdzanie uprawnień użytkowników

## Scenariusze Testowe

### Scenariusz 1: Pierwszy Kanał RSS
1. Aktywuj wtyczkę
2. Przejdź do RSS Aggregator → Add New
3. Dodaj kanał TVN24 (https://tvn24.pl/rss)
4. Ustaw powiat "warszawski"
5. Kliknij "Test Feed" - powinno pokazać sukces
6. Zapisz kanał
7. Kliknij "Update Now" - powinno pobrać artykuły
8. Sprawdź czy utworzono posty w WordPress

### Scenariusz 2: Automatyczne Aktualizacje
1. Dodaj kanał z częstotliwością "Every 15 minutes"
2. Sprawdź w Status czy zadanie cron jest zaplanowane
3. Poczekaj 15 minut lub uruchom cron ręcznie
4. Sprawdź czy pojawiły się nowe posty

### Scenariusz 3: Integracja z BuddyBoss
1. Upewnij się, że BuddyBoss jest aktywny
2. Włącz integrację w ustawieniach
3. Dodaj kanał RSS i pobierz artykuły
4. Sprawdź strumień aktywności BuddyBoss
5. Sprawdź czy wpisy RSS są widoczne

### Scenariusz 4: Test Pierwszego Importu
1. Dodaj nowy kanał RSS z dużą liczbą wpisów (np. TVN24)
2. Ustaw "Initial Import Count" na 5
3. Zapisz kanał i kliknij "Update Now"
4. Sprawdź czy utworzono dokładnie 5 najnowszych postów
5. Sprawdź czy posty są sortowane od najnowszego
6. Uruchom ponownie aktualizację - powinny pojawić się nowe wpisy (jeśli są)

### Scenariusz 5: Błędne Kanały
1. Spróbuj dodać niepoprawny URL RSS
2. Sprawdź czy "Test Feed" pokazuje błąd
3. Spróbuj dodać URL który nie istnieje
4. Sprawdź obsługę błędów

## Częste Problemy i Rozwiązania

### Problem: "Test Feed" zawsze pokazuje błąd
**Sprawdź:**
- Czy serwer ma dostęp do internetu
- Czy PHP ma rozszerzenie `curl`
- Czy URL RSS jest poprawny
- Czy nie ma problemów z SSL

### Problem: Cron nie działa
**Sprawdź:**
- Czy WP-Cron jest włączony
- Czy strona ma ruch (WP-Cron uruchamia się przy odwiedzinach)
- Czy hosting nie blokuje WP-Cron

### Problem: Nie pobierają się miniaturki
**Sprawdź:**
- Czy włączone jest "Enable Thumbnails"
- Czy serwer może pobierać obrazki z zewnętrznych URL
- Czy kanał RSS zawiera informacje o obrazkach

### Problem: Duplikaty postów
**Sprawdź:**
- Czy GUID w RSS jest unikalny
- Czy nie ma problemów z parsowaniem dat
- Sprawdź tabele `wp_rss_aggregator_items`

## Logi i Debugowanie

### Włączenie Logów WordPress
```php
// Dodaj do wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

### Logi Wtyczki
Wtyczka zapisuje logi do:
- WordPress error log
- PHP error log
- Sprawdź `/wp-content/debug.log`

### Przydatne Zapytania SQL
```sql
-- Sprawdź kanały RSS
SELECT * FROM wp_rss_aggregator_feeds;

-- Sprawdź elementy RSS
SELECT * FROM wp_rss_aggregator_items ORDER BY created_at DESC LIMIT 10;

-- Sprawdź posty RSS
SELECT p.*, pm.meta_value as feed_id 
FROM wp_posts p 
JOIN wp_postmeta pm ON p.ID = pm.post_id 
WHERE pm.meta_key = '_rss_aggregator_feed_id' 
ORDER BY p.post_date DESC LIMIT 10;
```

## Testy Automatyczne

### Testowanie przez WP-CLI (jeśli dostępne)
```bash
# Sprawdź status wtyczki
wp plugin status rss-aggregator

# Uruchom cron ręcznie
wp cron event run rss_aggregator_check_feeds

# Sprawdź zaplanowane zadania
wp cron event list
```

### Testowanie przez URL
```
# Test cron (jeśli WP-Cron jest włączony)
https://twoja-domena.pl/wp-cron.php?doing_wp_cron

# Test RSS (sprawdź czy WordPress generuje RSS)
https://twoja-domena.pl/feed/
```
