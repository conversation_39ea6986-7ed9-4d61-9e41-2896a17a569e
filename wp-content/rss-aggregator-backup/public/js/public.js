/**
 * RSS Aggregator Public JavaScript
 *
 * @package RSS_Aggregator
 * @since 1.0.0
 */

(function($) {
    'use strict';
    
    // Main public object
    var RSSAggregatorPublic = {
        
        /**
         * Initialize public functionality
         */
        init: function() {
            this.bindEvents();
            this.initLazyLoading();
            this.initAnalytics();
        },
        
        /**
         * Bind event handlers
         */
        bindEvents: function() {
            // Track external link clicks
            $(document).on('click', '.rss-original-link', this.trackExternalClick);
            
            // <PERSON>le read more clicks
            $(document).on('click', '.rss-read-more', this.trackReadMore);
            
            // Handle thumbnail clicks
            $(document).on('click', '.rss-item-thumbnail img', this.handleThumbnailClick);
            
            // Handle source info toggle on mobile
            $(document).on('click', '.rss-source-header', this.toggleSourceInfo);
        },
        
        /**
         * Initialize lazy loading for RSS images
         */
        initLazyLoading: function() {
            if ('IntersectionObserver' in window) {
                var imageObserver = new IntersectionObserver(function(entries, observer) {
                    entries.forEach(function(entry) {
                        if (entry.isIntersecting) {
                            var img = entry.target;
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            imageObserver.unobserve(img);
                        }
                    });
                });
                
                $('.rss-aggregator-item img.lazy, .rss-recent-item img.lazy').each(function() {
                    imageObserver.observe(this);
                });
            }
        },
        
        /**
         * Initialize analytics tracking
         */
        initAnalytics: function() {
            // Track RSS post views
            if ($('body').hasClass('single') && $('.rss-aggregator-source-info').length) {
                this.trackRSSPostView();
            }
        },
        
        /**
         * Track external link clicks
         */
        trackExternalClick: function(e) {
            var $link = $(this);
            var url = $link.attr('href');
            var feedSource = $link.closest('.rss-aggregator-item, .rss-aggregator-source-info')
                                  .find('.rss-item-source, .rss-source-details strong')
                                  .text();
            
            // Track with Google Analytics if available
            if (typeof gtag !== 'undefined') {
                gtag('event', 'click', {
                    'event_category': 'RSS Aggregator',
                    'event_label': 'External Link - ' + feedSource,
                    'value': url
                });
            }
            
            // Track with custom analytics
            RSSAggregatorPublic.sendAnalytics('external_click', {
                url: url,
                source: feedSource,
                post_id: $('body').hasClass('single') ? $('body').attr('class').match(/postid-(\d+)/)?.[1] : null
            });
        },
        
        /**
         * Track read more clicks
         */
        trackReadMore: function(e) {
            var $link = $(this);
            var postTitle = $link.closest('.rss-aggregator-item')
                                 .find('.rss-item-title')
                                 .text();
            
            // Track with Google Analytics if available
            if (typeof gtag !== 'undefined') {
                gtag('event', 'click', {
                    'event_category': 'RSS Aggregator',
                    'event_label': 'Read More - ' + postTitle
                });
            }
            
            // Track with custom analytics
            RSSAggregatorPublic.sendAnalytics('read_more_click', {
                post_title: postTitle
            });
        },
        
        /**
         * Handle thumbnail clicks
         */
        handleThumbnailClick: function(e) {
            var $img = $(this);
            var $item = $img.closest('.rss-aggregator-item, .rss-recent-item');
            var $titleLink = $item.find('.rss-item-title a, .rss-recent-content h4 a');
            
            if ($titleLink.length) {
                window.location.href = $titleLink.attr('href');
            }
        },
        
        /**
         * Toggle source info on mobile
         */
        toggleSourceInfo: function(e) {
            if (window.innerWidth <= 768) {
                var $sourceInfo = $(this).closest('.rss-aggregator-source-info');
                var $details = $sourceInfo.find('.rss-source-details');
                
                $details.slideToggle(200);
                $sourceInfo.toggleClass('expanded');
            }
        },
        
        /**
         * Track RSS post view
         */
        trackRSSPostView: function() {
            var postId = $('body').attr('class').match(/postid-(\d+)/)?.[1];
            var feedSource = $('.rss-source-details strong').text();
            
            if (postId && feedSource) {
                // Track with Google Analytics if available
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'page_view', {
                        'event_category': 'RSS Aggregator',
                        'event_label': 'RSS Post View - ' + feedSource,
                        'custom_map': {
                            'dimension1': feedSource
                        }
                    });
                }
                
                // Track with custom analytics
                this.sendAnalytics('rss_post_view', {
                    post_id: postId,
                    source: feedSource
                });
            }
        },
        
        /**
         * Send custom analytics data
         */
        sendAnalytics: function(event, data) {
            // Only send if analytics is enabled and we have AJAX URL
            if (!rssAggregatorPublic || !rssAggregatorPublic.ajaxUrl) {
                return;
            }
            
            $.post(rssAggregatorPublic.ajaxUrl, {
                action: 'rss_aggregator_track_event',
                nonce: rssAggregatorPublic.nonce,
                event: event,
                data: data
            }).fail(function() {
                // Silently fail - don't interrupt user experience
                console.log('RSS Aggregator: Failed to send analytics data');
            });
        },
        
        /**
         * Format relative time
         */
        formatRelativeTime: function(timestamp) {
            var now = new Date().getTime();
            var diff = now - timestamp;
            
            var seconds = Math.floor(diff / 1000);
            var minutes = Math.floor(seconds / 60);
            var hours = Math.floor(minutes / 60);
            var days = Math.floor(hours / 24);
            
            if (days > 0) {
                return days + (days === 1 ? ' day ago' : ' days ago');
            } else if (hours > 0) {
                return hours + (hours === 1 ? ' hour ago' : ' hours ago');
            } else if (minutes > 0) {
                return minutes + (minutes === 1 ? ' minute ago' : ' minutes ago');
            } else {
                return 'Just now';
            }
        },
        
        /**
         * Update relative timestamps
         */
        updateRelativeTimestamps: function() {
            $('.rss-item-date[data-timestamp], .rss-recent-date[data-timestamp]').each(function() {
                var $element = $(this);
                var timestamp = parseInt($element.data('timestamp')) * 1000;
                var relativeTime = RSSAggregatorPublic.formatRelativeTime(timestamp);
                $element.text(relativeTime);
            });
        },
        
        /**
         * Initialize infinite scroll for RSS feeds
         */
        initInfiniteScroll: function() {
            var $container = $('.rss-aggregator-feed-display');
            var $loadMore = $('.rss-load-more-button');
            
            if ($container.length && $loadMore.length) {
                var loading = false;
                var page = 2;
                
                $loadMore.on('click', function(e) {
                    e.preventDefault();
                    
                    if (loading) return;
                    
                    loading = true;
                    var $button = $(this);
                    var originalText = $button.text();
                    
                    $button.text('Loading...').prop('disabled', true);
                    
                    $.post(rssAggregatorPublic.ajaxUrl, {
                        action: 'rss_aggregator_load_more',
                        nonce: rssAggregatorPublic.nonce,
                        page: page,
                        feed_id: $container.data('feed-id'),
                        county: $container.data('county')
                    })
                    .done(function(response) {
                        if (response.success && response.data.html) {
                            $container.append(response.data.html);
                            page++;
                            
                            if (!response.data.has_more) {
                                $button.hide();
                            }
                        } else {
                            $button.hide();
                        }
                    })
                    .fail(function() {
                        $button.text('Error loading more posts');
                    })
                    .always(function() {
                        loading = false;
                        $button.text(originalText).prop('disabled', false);
                    });
                });
            }
        },
        
        /**
         * Add smooth scrolling to RSS anchors
         */
        initSmoothScrolling: function() {
            $('a[href^="#rss-"]').on('click', function(e) {
                e.preventDefault();
                
                var target = $(this.getAttribute('href'));
                if (target.length) {
                    $('html, body').animate({
                        scrollTop: target.offset().top - 100
                    }, 500);
                }
            });
        },
        
        /**
         * Initialize RSS feed refresh
         */
        initFeedRefresh: function() {
            var $refreshButton = $('.rss-refresh-button');
            
            $refreshButton.on('click', function(e) {
                e.preventDefault();
                
                var $button = $(this);
                var $container = $button.closest('.rss-aggregator-feed-display');
                var originalText = $button.text();
                
                $button.text('Refreshing...').prop('disabled', true);
                $container.addClass('rss-loading');
                
                $.post(rssAggregatorPublic.ajaxUrl, {
                    action: 'rss_aggregator_refresh_feed',
                    nonce: rssAggregatorPublic.nonce,
                    feed_id: $container.data('feed-id')
                })
                .done(function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('Failed to refresh feed');
                    }
                })
                .fail(function() {
                    alert('Failed to refresh feed');
                })
                .always(function() {
                    $button.text(originalText).prop('disabled', false);
                    $container.removeClass('rss-loading');
                });
            });
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        RSSAggregatorPublic.init();
        RSSAggregatorPublic.initInfiniteScroll();
        RSSAggregatorPublic.initSmoothScrolling();
        RSSAggregatorPublic.initFeedRefresh();
        
        // Update relative timestamps every minute
        setInterval(function() {
            RSSAggregatorPublic.updateRelativeTimestamps();
        }, 60000);
    });
    
    // Make available globally
    window.RSSAggregatorPublic = RSSAggregatorPublic;
    
})(jQuery);
