<?php
/**
 * Manual Database Migration Script for RSS Aggregator
 * 
 * Run this file to manually update the database structure
 * Access via: /wp-content/plugins/rss-aggregator/migrate-database.php
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. You must be an administrator to run this script.');
}

echo "<h1>RSS Aggregator Database Migration</h1>";

global $wpdb;

// Define table name
$feeds_table = $wpdb->prefix . 'rss_aggregator_feeds';

echo "<h2>Current Table Structure</h2>";

// Show current table structure
$columns = $wpdb->get_results("SHOW COLUMNS FROM {$feeds_table}");
echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
foreach ($columns as $column) {
    echo "<tr>";
    echo "<td>{$column->Field}</td>";
    echo "<td>{$column->Type}</td>";
    echo "<td>{$column->Null}</td>";
    echo "<td>{$column->Key}</td>";
    echo "<td>{$column->Default}</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h2>Migration Steps</h2>";

// Check and add missing columns
$migrations = array(
    'geodirectory_place_name' => "ALTER TABLE {$feeds_table} ADD COLUMN geodirectory_place_name varchar(255) DEFAULT NULL AFTER geodirectory_place_id",
    'assigned_user_id' => "ALTER TABLE {$feeds_table} ADD COLUMN assigned_user_id bigint(20) DEFAULT NULL AFTER geodirectory_place_name",
    'assigned_user_name' => "ALTER TABLE {$feeds_table} ADD COLUMN assigned_user_name varchar(255) DEFAULT NULL AFTER assigned_user_id"
);

foreach ($migrations as $column => $sql) {
    echo "<h3>Checking column: {$column}</h3>";
    
    $column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$feeds_table} LIKE '{$column}'");
    
    if (empty($column_exists)) {
        echo "<p style='color: orange;'>Column {$column} does not exist. Adding...</p>";
        
        $result = $wpdb->query($sql);
        
        if ($result !== false) {
            echo "<p style='color: green;'>✓ Successfully added column {$column}</p>";
        } else {
            echo "<p style='color: red;'>✗ Failed to add column {$column}</p>";
            echo "<p>Error: " . $wpdb->last_error . "</p>";
        }
    } else {
        echo "<p style='color: green;'>✓ Column {$column} already exists</p>";
    }
}

// Add indexes
echo "<h3>Adding indexes</h3>";

$indexes = array(
    'idx_assigned_user' => "ALTER TABLE {$feeds_table} ADD INDEX idx_assigned_user (assigned_user_id)"
);

foreach ($indexes as $index_name => $sql) {
    // Check if index exists
    $index_exists = $wpdb->get_results("SHOW INDEX FROM {$feeds_table} WHERE Key_name = '{$index_name}'");
    
    if (empty($index_exists)) {
        echo "<p style='color: orange;'>Index {$index_name} does not exist. Adding...</p>";
        
        $result = $wpdb->query($sql);
        
        if ($result !== false) {
            echo "<p style='color: green;'>✓ Successfully added index {$index_name}</p>";
        } else {
            echo "<p style='color: red;'>✗ Failed to add index {$index_name}</p>";
            echo "<p>Error: " . $wpdb->last_error . "</p>";
        }
    } else {
        echo "<p style='color: green;'>✓ Index {$index_name} already exists</p>";
    }
}

// Update database version
update_option('rss_aggregator_db_version', '1.2.0');
echo "<p style='color: green;'>✓ Updated database version to 1.2.0</p>";

echo "<h2>Final Table Structure</h2>";

// Show final table structure
$columns = $wpdb->get_results("SHOW COLUMNS FROM {$feeds_table}");
echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
foreach ($columns as $column) {
    echo "<tr>";
    echo "<td>{$column->Field}</td>";
    echo "<td>{$column->Type}</td>";
    echo "<td>{$column->Null}</td>";
    echo "<td>{$column->Key}</td>";
    echo "<td>{$column->Default}</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h2>Migration Complete!</h2>";
echo "<p>You can now go back to the RSS Aggregator admin panel and try adding a feed again.</p>";
echo "<p><a href='" . admin_url('admin.php?page=rss-aggregator') . "'>Go to RSS Aggregator</a></p>";

// Test the database
echo "<h2>Database Test</h2>";
$test_data = array(
    'name' => 'Test Feed',
    'url' => 'https://example.com/rss',
    'geodirectory_place_name' => 'Test Place',
    'assigned_user_name' => 'Test User',
    'county' => 'Test County',
    'update_frequency' => 'hourly',
    'initial_import_count' => 10,
    'status' => 'active'
);

echo "<p>Testing insert with new columns...</p>";
$result = $wpdb->insert($feeds_table, $test_data);

if ($result !== false) {
    echo "<p style='color: green;'>✓ Database test successful!</p>";
    
    // Clean up test data
    $wpdb->delete($feeds_table, array('name' => 'Test Feed'));
    echo "<p>Test data cleaned up.</p>";
} else {
    echo "<p style='color: red;'>✗ Database test failed!</p>";
    echo "<p>Error: " . $wpdb->last_error . "</p>";
}
?>
