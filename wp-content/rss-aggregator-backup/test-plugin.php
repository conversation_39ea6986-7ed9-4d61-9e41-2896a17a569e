<?php
/**
 * Test file for RSS Aggregator Plugin
 * 
 * This file can be used to test if the plugin loads correctly
 * Access via: /wp-content/plugins/rss-aggregator/test-plugin.php
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress
    require_once('../../../wp-load.php');
}

echo "<h1>RSS Aggregator Plugin Test</h1>";

// Test 1: Check if plugin constants are defined
echo "<h2>1. Plugin Constants</h2>";
echo "RSS_AGGREGATOR_VERSION: " . (defined('RSS_AGGREGATOR_VERSION') ? RSS_AGGREGATOR_VERSION : 'NOT DEFINED') . "<br>";
echo "RSS_AGGREGATOR_PLUGIN_DIR: " . (defined('RSS_AGGREGATOR_PLUGIN_DIR') ? RSS_AGGREGATOR_PLUGIN_DIR : 'NOT DEFINED') . "<br>";
echo "RSS_AGGREGATOR_FEEDS_TABLE: " . (defined('RSS_AGGREGATOR_FEEDS_TABLE') ? RSS_AGGREGATOR_FEEDS_TABLE : 'NOT DEFINED') . "<br>";

// Test 2: Check if classes exist
echo "<h2>2. Classes</h2>";
echo "RSS_Aggregator_Plugin: " . (class_exists('RSS_Aggregator_Plugin') ? 'EXISTS' : 'NOT EXISTS') . "<br>";
echo "RSS_Aggregator: " . (class_exists('RSS_Aggregator') ? 'EXISTS' : 'NOT EXISTS') . "<br>";
echo "RSS_Database: " . (class_exists('RSS_Database') ? 'EXISTS' : 'NOT EXISTS') . "<br>";
echo "RSS_Admin: " . (class_exists('RSS_Admin') ? 'EXISTS' : 'NOT EXISTS') . "<br>";

// Test 3: Check database tables
echo "<h2>3. Database Tables</h2>";
global $wpdb;

if (defined('RSS_AGGREGATOR_FEEDS_TABLE')) {
    $feeds_table_exists = $wpdb->get_var("SHOW TABLES LIKE '" . RSS_AGGREGATOR_FEEDS_TABLE . "'");
    echo "Feeds table: " . ($feeds_table_exists ? 'EXISTS' : 'NOT EXISTS') . "<br>";
    
    if ($feeds_table_exists) {
        $feeds_count = $wpdb->get_var("SELECT COUNT(*) FROM " . RSS_AGGREGATOR_FEEDS_TABLE);
        echo "Feeds count: " . $feeds_count . "<br>";
    }
}

if (defined('RSS_AGGREGATOR_ITEMS_TABLE')) {
    $items_table_exists = $wpdb->get_var("SHOW TABLES LIKE '" . RSS_AGGREGATOR_ITEMS_TABLE . "'");
    echo "Items table: " . ($items_table_exists ? 'EXISTS' : 'NOT EXISTS') . "<br>";
    
    if ($items_table_exists) {
        $items_count = $wpdb->get_var("SELECT COUNT(*) FROM " . RSS_AGGREGATOR_ITEMS_TABLE);
        echo "Items count: " . $items_count . "<br>";
    }
}

// Test 4: Check WordPress hooks
echo "<h2>4. WordPress Hooks</h2>";
echo "admin_menu hook: " . (has_action('admin_menu') ? 'REGISTERED' : 'NOT REGISTERED') . "<br>";

// Test 5: Check plugin options
echo "<h2>5. Plugin Options</h2>";
$options = array(
    'rss_aggregator_db_version',
    'rss_aggregator_default_frequency',
    'rss_aggregator_max_items_per_feed',
    'rss_aggregator_default_initial_count'
);

foreach ($options as $option) {
    $value = get_option($option, 'NOT SET');
    echo "{$option}: {$value}<br>";
}

// Test 6: Test RSS_Admin instance
echo "<h2>6. RSS_Admin Instance</h2>";
if (class_exists('RSS_Admin')) {
    try {
        $admin_instance = RSS_Admin::get_instance();
        echo "RSS_Admin instance: " . (is_object($admin_instance) ? 'SUCCESS' : 'FAILED') . "<br>";
        
        if (is_object($admin_instance)) {
            echo "RSS_Admin class: " . get_class($admin_instance) . "<br>";
            
            // Check if methods exist
            $methods = array('add_admin_menu', 'display_feeds_page', 'display_admin_page');
            foreach ($methods as $method) {
                echo "Method {$method}: " . (method_exists($admin_instance, $method) ? 'EXISTS' : 'NOT EXISTS') . "<br>";
            }
        }
    } catch (Exception $e) {
        echo "RSS_Admin error: " . $e->getMessage() . "<br>";
    }
} else {
    echo "RSS_Admin class not found<br>";
}

echo "<h2>Test Complete</h2>";
echo "<p>If you see errors above, check the WordPress error log for more details.</p>";
?>
