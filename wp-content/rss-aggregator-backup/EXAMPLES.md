# RSS Aggregator - Prz<PERSON><PERSON>dy Użycia

## Shortcodes

### 1. Podstawowe Wyświetlanie Kanałów RSS

#### Wszystkie kanały RSS (najnowsze 10 wpisów)
```php
[rss_aggregator_feed limit="10"]
```

#### Konkretny kanał RSS
```php
[rss_aggregator_feed feed_id="1" limit="5" show_thumbnail="yes"]
```

#### Kanały z konkretnego powiatu
```php
[rss_aggregator_county county="krakowski" limit="8" show_excerpt="yes"]
```

#### Najnowsze wpisy z ostatnich 7 dni
```php
[rss_aggregator_recent limit="5" days="7" show_date="yes" show_source="yes"]
```

### 2. Zaawansowane Shortcodes

#### Kanał bez miniaturek i excerptów
```php
[rss_aggregator_feed feed_id="2" limit="15" show_thumbnail="no" show_excerpt="no" show_source="yes"]
```

#### Lista kompaktowa z datami
```php
[rss_aggregator_recent limit="10" days="3" show_thumbnail="no" show_excerpt="no" show_date="yes" class="compact-list"]
```

#### Kanały z konkretnego powiatu z miniaturkami
```php
[rss_aggregator_county county="gdański" limit="6" show_thumbnail="yes" show_excerpt="yes" show_date="yes"]
```

## Przykłady Integracji w Szablonach

### 1. Wyświetlanie w Sidebar

#### functions.php
```php
// Dodaj widget area dla RSS
function theme_widgets_init() {
    register_sidebar(array(
        'name'          => 'RSS Sidebar',
        'id'            => 'rss-sidebar',
        'before_widget' => '<div class="rss-widget">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="rss-widget-title">',
        'after_title'   => '</h3>',
    ));
}
add_action('widgets_init', 'theme_widgets_init');
```

#### sidebar-rss.php
```php
<aside class="rss-sidebar">
    <h2>Najnowsze Wiadomości</h2>
    <?php echo do_shortcode('[rss_aggregator_recent limit="5" show_thumbnail="no" show_excerpt="no"]'); ?>
</aside>
```

### 2. Strona Dedykowana dla RSS

#### page-news.php
```php
<?php get_header(); ?>

<div class="news-page">
    <div class="container">
        <h1>Wiadomości Lokalne</h1>
        
        <!-- Najnowsze wiadomości -->
        <section class="latest-news">
            <h2>Najnowsze</h2>
            <?php echo do_shortcode('[rss_aggregator_recent limit="8" days="2" show_thumbnail="yes"]'); ?>
        </section>
        
        <!-- Wiadomości z Krakowa -->
        <section class="krakow-news">
            <h2>Powiat Krakowski</h2>
            <?php echo do_shortcode('[rss_aggregator_county county="krakowski" limit="6"]'); ?>
        </section>
        
        <!-- Wiadomości z Gdańska -->
        <section class="gdansk-news">
            <h2>Powiat Gdański</h2>
            <?php echo do_shortcode('[rss_aggregator_county county="gdański" limit="6"]'); ?>
        </section>
    </div>
</div>

<?php get_footer(); ?>
```

### 3. Integracja z BuddyBoss

#### Niestandardowy Hook dla Aktywności
```php
// Dodaj niestandardowe informacje do aktywności RSS
function custom_rss_activity_content($content) {
    global $activities_template;
    
    if (empty($activities_template->activity)) {
        return $content;
    }
    
    $activity = $activities_template->activity;
    $feed_id = bp_activity_get_meta($activity->id, 'rss_aggregator_feed_id');
    
    if (!empty($feed_id)) {
        $county = bp_activity_get_meta($activity->id, 'rss_aggregator_county');
        if (!empty($county)) {
            $content .= '<div class="rss-activity-footer">';
            $content .= '<span class="rss-county">📍 Powiat ' . esc_html($county) . '</span>';
            $content .= '</div>';
        }
    }
    
    return $content;
}
add_filter('bp_get_activity_content_body', 'custom_rss_activity_content');
```

## Przykłady CSS

### 1. Stylowanie Shortcodów

```css
/* Podstawowe style dla kanałów RSS */
.rss-aggregator-feed-display {
    margin: 20px 0;
}

.rss-aggregator-item {
    border-bottom: 1px solid #eee;
    padding-bottom: 20px;
    margin-bottom: 20px;
}

.rss-aggregator-item:last-child {
    border-bottom: none;
}

/* Kompaktowa lista */
.compact-list .rss-aggregator-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.compact-list .rss-item-content {
    flex: 1;
}

.compact-list .rss-item-title {
    font-size: 16px;
    margin: 0;
}

.compact-list .rss-item-meta {
    font-size: 12px;
    color: #666;
}

/* Siatka dla powiatów */
.county-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.county-section {
    background: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
}

.county-section h3 {
    margin-top: 0;
    color: #333;
    border-bottom: 2px solid #007cba;
    padding-bottom: 10px;
}
```

### 2. Responsywne Style

```css
/* Tablet */
@media (max-width: 768px) {
    .rss-aggregator-item {
        flex-direction: column;
    }
    
    .rss-item-thumbnail {
        margin-right: 0;
        margin-bottom: 15px;
        width: 100%;
    }
    
    .county-grid {
        grid-template-columns: 1fr;
    }
}

/* Mobile */
@media (max-width: 480px) {
    .rss-item-title {
        font-size: 16px;
    }
    
    .rss-item-meta {
        flex-direction: column;
        gap: 5px;
    }
    
    .rss-item-actions {
        flex-direction: column;
        gap: 10px;
    }
}
```

## Przykłady PHP

### 1. Programowe Dodawanie Kanałów

```php
// Dodaj kanał RSS programowo
function add_rss_feed_programmatically() {
    $database = new RSS_Database();

    $feed_data = array(
        'name' => 'Portal Krakowski',
        'url' => 'https://krakow.pl/rss',
        'county' => 'krakowski',
        'update_frequency' => 'hourly',
        'initial_import_count' => 15, // Pobierz 15 najnowszych wpisów przy pierwszym imporcie
        'status' => 'active'
    );

    $feed_id = $database->save_feed($feed_data);

    if ($feed_id) {
        // Zaplanuj aktualizacje
        $cron = new RSS_Cron();
        $cron->schedule_feed_update($feed_id, 'hourly');

        // Uruchom pierwszy import
        $aggregator = RSS_Aggregator::get_instance();
        $result = $aggregator->update_single_feed($feed_id, true); // true = initial import

        return $feed_id;
    }

    return false;
}
```

### 2. Niestandardowe Wyświetlanie

```php
// Funkcja do wyświetlania RSS w szablonie
function display_custom_rss_feed($county = '', $limit = 5) {
    $database = new RSS_Database();
    
    $args = array(
        'post_type' => 'post',
        'posts_per_page' => $limit,
        'meta_query' => array(
            array(
                'key' => '_rss_aggregator_feed_id',
                'compare' => 'EXISTS'
            )
        )
    );
    
    if (!empty($county)) {
        $args['meta_query'][] = array(
            'key' => '_rss_aggregator_county',
            'value' => $county,
            'compare' => '='
        );
    }
    
    $query = new WP_Query($args);
    
    if ($query->have_posts()) {
        echo '<div class="custom-rss-display">';
        while ($query->have_posts()) {
            $query->the_post();
            
            $feed_id = get_post_meta(get_the_ID(), '_rss_aggregator_feed_id', true);
            $source_url = get_post_meta(get_the_ID(), '_rss_aggregator_source_url', true);
            
            echo '<article class="rss-item">';
            echo '<h3><a href="' . get_permalink() . '">' . get_the_title() . '</a></h3>';
            echo '<div class="rss-excerpt">' . get_the_excerpt() . '</div>';
            if ($source_url) {
                echo '<a href="' . esc_url($source_url) . '" target="_blank">Czytaj oryginał</a>';
            }
            echo '</article>';
        }
        echo '</div>';
        wp_reset_postdata();
    }
}

// Użycie w szablonie
display_custom_rss_feed('krakowski', 8);
```

### 3. Kontrola Pierwszego Importu

```php
// Hook do modyfikacji liczby wpisów przy pierwszym imporcie
function custom_initial_import_count($count, $feed) {
    // Dla kanałów z dużą aktywnością, ogranicz do 5 wpisów
    $high_activity_feeds = array(
        'https://tvn24.pl/rss',
        'https://wiadomosci.onet.pl/rss'
    );

    if (in_array($feed->url, $high_activity_feeds)) {
        return 5;
    }

    // Dla kanałów lokalnych, pobierz więcej
    if (!empty($feed->county)) {
        return 20;
    }

    return $count; // Użyj domyślnej wartości
}
add_filter('rss_aggregator_initial_import_count', 'custom_initial_import_count', 10, 2);

// Funkcja do sprawdzenia czy kanał był już importowany
function is_feed_imported($feed_id) {
    $database = new RSS_Database();
    $feed = $database->get_feed($feed_id);

    return !empty($feed->last_updated);
}

// Funkcja do ręcznego uruchomienia pierwszego importu
function manual_initial_import($feed_id, $custom_count = null) {
    $database = new RSS_Database();
    $feed = $database->get_feed($feed_id);

    if (!$feed) {
        return false;
    }

    // Tymczasowo zmień liczbę importu
    if ($custom_count !== null) {
        $original_count = $feed->initial_import_count;
        $database->save_feed(array(
            'id' => $feed_id,
            'initial_import_count' => $custom_count
        ));
    }

    // Wyczyść last_updated aby wymusić pierwszy import
    global $wpdb;
    $wpdb->update(
        RSS_AGGREGATOR_FEEDS_TABLE,
        array('last_updated' => null),
        array('id' => $feed_id),
        array('%s'),
        array('%d')
    );

    // Uruchom import
    $aggregator = RSS_Aggregator::get_instance();
    $result = $aggregator->update_single_feed($feed_id, true);

    // Przywróć oryginalną liczbę
    if ($custom_count !== null && isset($original_count)) {
        $database->save_feed(array(
            'id' => $feed_id,
            'initial_import_count' => $original_count
        ));
    }

    return $result;
}

// Użycie:
// manual_initial_import(1, 25); // Importuj 25 najnowszych wpisów z kanału ID 1
```

### 4. Hook do Modyfikacji Treści RSS

```php
// Dodaj niestandardowe informacje do treści postów RSS
function modify_rss_post_content($content) {
    global $post;
    
    $feed_id = get_post_meta($post->ID, '_rss_aggregator_feed_id', true);
    
    if (!empty($feed_id)) {
        $database = new RSS_Database();
        $feed = $database->get_feed($feed_id);
        $source_url = get_post_meta($post->ID, '_rss_aggregator_source_url', true);
        
        $additional_info = '<div class="rss-source-info">';
        $additional_info .= '<p><strong>Źródło:</strong> ' . esc_html($feed->name) . '</p>';
        if ($source_url) {
            $additional_info .= '<p><a href="' . esc_url($source_url) . '" target="_blank">Czytaj pełny artykuł</a></p>';
        }
        $additional_info .= '</div>';
        
        $content .= $additional_info;
    }
    
    return $content;
}
add_filter('the_content', 'modify_rss_post_content');
```

## Przykłady JavaScript

### 1. Dynamiczne Ładowanie Więcej Postów

```javascript
// Dodaj przycisk "Załaduj więcej" do shortcodów
jQuery(document).ready(function($) {
    $('.rss-aggregator-feed-display').each(function() {
        var $container = $(this);
        var page = 2;
        
        $container.append('<button class="load-more-rss" data-page="2">Załaduj więcej</button>');
        
        $('.load-more-rss').on('click', function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var currentPage = $button.data('page');
            
            $button.text('Ładowanie...').prop('disabled', true);
            
            $.post(ajax_object.ajax_url, {
                action: 'load_more_rss_posts',
                page: currentPage,
                nonce: ajax_object.nonce
            }, function(response) {
                if (response.success) {
                    $container.find('.load-more-rss').before(response.data.html);
                    $button.data('page', currentPage + 1);
                    
                    if (!response.data.has_more) {
                        $button.hide();
                    }
                }
                
                $button.text('Załaduj więcej').prop('disabled', false);
            });
        });
    });
});
```

### 2. Filtrowanie po Powiatach

```javascript
// Dodaj filtry powiatów
jQuery(document).ready(function($) {
    // Dodaj filtry
    $('.rss-feed-container').prepend(`
        <div class="rss-filters">
            <label>Filtruj po powiecie:</label>
            <select id="county-filter">
                <option value="">Wszystkie</option>
                <option value="krakowski">Krakowski</option>
                <option value="gdański">Gdański</option>
                <option value="wrocławski">Wrocławski</option>
            </select>
        </div>
    `);
    
    // Obsługa filtrowania
    $('#county-filter').on('change', function() {
        var selectedCounty = $(this).val();
        
        $('.rss-aggregator-item').each(function() {
            var itemCounty = $(this).find('.rss-item-county').text().toLowerCase();
            
            if (selectedCounty === '' || itemCounty.includes(selectedCounty)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });
});
```

## Przykłady Integracji z Innymi Wtyczkami

### 1. Integracja z Elementor

```php
// Dodaj widget Elementor dla RSS
class RSS_Aggregator_Elementor_Widget extends \Elementor\Widget_Base {
    
    public function get_name() {
        return 'rss_aggregator';
    }
    
    public function get_title() {
        return 'RSS Aggregator';
    }
    
    public function get_icon() {
        return 'fa fa-rss';
    }
    
    protected function _register_controls() {
        $this->start_controls_section(
            'content_section',
            [
                'label' => 'Ustawienia RSS',
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            ]
        );
        
        $this->add_control(
            'county',
            [
                'label' => 'Powiat',
                'type' => \Elementor\Controls_Manager::TEXT,
                'placeholder' => 'np. krakowski',
            ]
        );
        
        $this->add_control(
            'limit',
            [
                'label' => 'Liczba postów',
                'type' => \Elementor\Controls_Manager::NUMBER,
                'default' => 5,
            ]
        );
        
        $this->end_controls_section();
    }
    
    protected function render() {
        $settings = $this->get_settings_for_display();
        
        $shortcode = '[rss_aggregator_county';
        if (!empty($settings['county'])) {
            $shortcode .= ' county="' . esc_attr($settings['county']) . '"';
        }
        $shortcode .= ' limit="' . intval($settings['limit']) . '"]';
        
        echo do_shortcode($shortcode);
    }
}

// Rejestracja widget
function register_rss_aggregator_elementor_widget() {
    \Elementor\Plugin::instance()->widgets_manager->register_widget_type(new RSS_Aggregator_Elementor_Widget());
}
add_action('elementor/widgets/widgets_registered', 'register_rss_aggregator_elementor_widget');
```

### 2. Integracja z WooCommerce

```php
// Dodaj RSS do strony sklepu
function add_rss_to_shop_page() {
    if (is_shop()) {
        echo '<div class="shop-news-section">';
        echo '<h3>Najnowsze Wiadomości</h3>';
        echo do_shortcode('[rss_aggregator_recent limit="3" show_thumbnail="yes" show_excerpt="no"]');
        echo '</div>';
    }
}
add_action('woocommerce_before_shop_loop', 'add_rss_to_shop_page');
```
