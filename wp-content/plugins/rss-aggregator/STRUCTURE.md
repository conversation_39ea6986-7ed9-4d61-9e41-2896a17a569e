# RSS Aggregator Pro - File Structure

## Core Files
```
rss-aggregator/
├── rss-aggregator.php              # Main plugin file
├── uninstall.php                   # Uninstall cleanup
├── README.md                       # Documentation
├── CHANGELOG.md                    # Version history
├── install.sql                     # Database setup
└── STRUCTURE.md                    # This file
```

## Includes Directory
```
includes/
├── class-rss-aggregator.php        # Main plugin class
├── class-rss-database.php          # Database operations
├── class-rss-cron.php             # Scheduled tasks
├── class-rss-thumbnail-extractor.php # Thumbnail extraction
└── constants.php                   # Plugin constants
```

## Admin Directory
```
admin/
├── class-rss-admin.php            # Admin interface
├── css/
│   └── admin.css                   # Admin styles
├── js/
│   └── admin.js                    # Admin JavaScript
└── partials/
    ├── feeds-list.php              # Feeds listing page
    ├── add-feed.php                # Add/edit feed form
    ├── settings.php                # Settings page
    ├── status.php                  # Status page
    ├── debug-page.php              # Debug tools
    └── feed-statistics.php         # Feed statistics
```

## Languages Directory
```
languages/
├── rss-aggregator.pot             # Translation template
├── rss-aggregator-pl_PL.po        # Polish translation
└── rss-aggregator-pl_PL.mo        # Compiled Polish translation
```

## Key Features Implemented

### ✅ Core Functionality
- RSS feed management (add, edit, delete)
- Automatic content import with scheduling
- Thumbnail extraction from multiple sources
- Place-based publishing system
- User assignment system
- Geographic filtering (county/region)

### ✅ Integrations
- GeoDirectory places integration
- BuddyBoss activity stream
- WordPress cron system
- WordPress media library

### ✅ Management Features
- Feed statistics and analytics
- Individual post management
- Debug tools and troubleshooting
- Bulk operations
- AJAX-powered interface

### ✅ Technical Features
- Dynamic table prefix detection
- Comprehensive error handling
- Security (nonce verification)
- Performance optimization
- Clean uninstall process

## Database Tables

### wp_rss_aggregator_feeds
- Feed configuration and metadata
- Place and user assignments
- Geographic information
- Update settings

### wp_rss_aggregator_items
- Individual RSS items
- Processing status
- WordPress post associations
- Metadata and thumbnails

## Author Priority System

1. **Place Owner** - If GeoDirectory place assigned, creates virtual user with place name
2. **Assigned User** - If user assigned to feed
3. **Default Author** - From plugin settings

## Installation Requirements

- WordPress 5.0+
- PHP 7.4+
- MySQL 5.7+
- GeoDirectory (optional)
- BuddyBoss (optional)

## Version Information

- **Current Version**: 2.0.0
- **Author**: Łukasz Nowak
- **License**: GPL v2 or later
- **Text Domain**: rss-aggregator

## Support

For support and updates:
- Website: https://foto-nowak.pl
- GitHub: https://github.com/luknow86/rss-aggregator-pro
