<?php
/**
 * RSS Database Class
 *
 * @package RSS_Aggregator
 * @version 2.0.0
 * <AUTHOR>
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class RSS_Database {
    
    private $wpdb;
    private $feeds_table;
    private $items_table;
    
    public function __construct() {
        global $wpdb;
        $this->wpdb = $wpdb;
        $this->feeds_table = $wpdb->prefix . 'rss_aggregator_feeds';
        $this->items_table = $wpdb->prefix . 'rss_aggregator_items';

        // Check if tables exist and create if needed
        $this->ensure_tables_exist();
    }

    /**
     * Ensure database tables exist
     */
    private function ensure_tables_exist() {
        $feeds_table_exists = $this->wpdb->get_var("SHOW TABLES LIKE '{$this->feeds_table}'") === $this->feeds_table;
        $items_table_exists = $this->wpdb->get_var("SHOW TABLES LIKE '{$this->items_table}'") === $this->items_table;

        if (!$feeds_table_exists || !$items_table_exists) {
            error_log('RSS Aggregator: Database tables missing. Feeds table exists: ' . ($feeds_table_exists ? 'yes' : 'no') . ', Items table exists: ' . ($items_table_exists ? 'yes' : 'no'));

            // You need to run the install.sql file to create the tables
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p><strong>RSS Aggregator:</strong> Database tables are missing. Please run the install.sql file in phpMyAdmin to create the required tables.</p></div>';
            });
        }
    }
    
    /**
     * Get all feeds
     */
    public function get_all_feeds() {
        return $this->wpdb->get_results("SELECT * FROM {$this->feeds_table} ORDER BY created_at DESC");
    }

    /**
     * Get feeds (alias for compatibility)
     */
    public function get_feeds() {
        return $this->get_all_feeds();
    }

    /**
     * Get feeds due for update
     */
    public function get_feeds_due_for_update() {
        // Use separate queries for different frequencies to avoid complex CASE in DATE_SUB
        $feeds = array();

        // Get all active feeds
        $all_feeds = $this->wpdb->get_results("
            SELECT * FROM {$this->feeds_table}
            WHERE status = 'active'
            ORDER BY last_updated ASC
        ");

        if (empty($all_feeds)) {
            return array();
        }

        $now = current_time('mysql');

        foreach ($all_feeds as $feed) {
            $should_update = false;

            // If never updated, always update
            if (empty($feed->last_updated) || $feed->last_updated === '0000-00-00 00:00:00') {
                $should_update = true;
            } else {
                // Calculate next update time based on frequency
                $last_updated = strtotime($feed->last_updated);
                $current_time = strtotime($now);

                $interval_seconds = $this->get_frequency_seconds($feed->update_frequency);
                $next_update = $last_updated + $interval_seconds;

                if ($current_time >= $next_update) {
                    $should_update = true;
                }
            }

            if ($should_update) {
                $feeds[] = $feed;
            }
        }

        return $feeds;
    }

    /**
     * Get frequency in seconds
     */
    private function get_frequency_seconds($frequency) {
        switch ($frequency) {
            case 'every_minute':
                return 60;
            case 'every_5_minutes':
                return 300;
            case 'every_15_minutes':
                return 900;
            case 'every_30_minutes':
                return 1800;
            case 'hourly':
                return 3600;
            case 'every_2_hours':
                return 7200;
            case 'every_6_hours':
                return 21600;
            case 'every_12_hours':
                return 43200;
            case 'daily':
                return 86400;
            case 'weekly':
                return 604800;
            default:
                return 3600; // Default to hourly
        }
    }
    
    /**
     * Get single feed
     */
    public function get_feed($feed_id) {
        return $this->wpdb->get_row($this->wpdb->prepare("SELECT * FROM {$this->feeds_table} WHERE id = %d", $feed_id));
    }
    
    /**
     * Save feed
     */
    public function save_feed($data) {
        $result = $this->wpdb->insert($this->feeds_table, $data);
        return $result ? $this->wpdb->insert_id : false;
    }
    
    /**
     * Update feed
     */
    public function update_feed($feed_id, $data) {
        return $this->wpdb->update($this->feeds_table, $data, array('id' => $feed_id));
    }

    /**
     * Update feed timestamp
     */
    public function update_feed_timestamp($feed_id) {
        return $this->wpdb->update(
            $this->feeds_table,
            array('last_updated' => current_time('mysql')),
            array('id' => $feed_id)
        );
    }
    
    /**
     * Delete feed
     */
    public function delete_feed($feed_id) {
        error_log("RSS Aggregator: Deleting feed {$feed_id} and all associated posts");

        // First delete all WordPress posts associated with this feed
        $this->delete_feed_posts($feed_id);

        // Delete associated RSS items
        $items_deleted = $this->wpdb->delete(
            $this->items_table,
            array('feed_id' => $feed_id),
            array('%d')
        );

        error_log("RSS Aggregator: Deleted {$items_deleted} RSS items for feed {$feed_id}");

        // Delete feed
        $result = $this->wpdb->delete(
            $this->feeds_table,
            array('id' => $feed_id),
            array('%d')
        );

        if ($result !== false) {
            error_log("RSS Aggregator: Successfully deleted feed {$feed_id}");
        } else {
            error_log("RSS Aggregator: Failed to delete feed {$feed_id}: " . $this->wpdb->last_error);
        }

        return $result !== false;
    }
    
    /**
     * Search places for autocomplete
     */
    public function search_places($search_term, $limit = 10) {
        $search_term = '%' . $this->wpdb->esc_like($search_term) . '%';
        
        $query = $this->wpdb->prepare("
            SELECT ID as id, post_title as name 
            FROM {$this->wpdb->posts} 
            WHERE post_type = 'gd_place' 
            AND post_status = 'publish' 
            AND post_title LIKE %s 
            ORDER BY post_title ASC 
            LIMIT %d
        ", $search_term, $limit);
        
        return $this->wpdb->get_results($query);
    }
    
    /**
     * Search users for autocomplete
     */
    public function search_users($search_term, $limit = 10) {
        $search_term = '%' . $this->wpdb->esc_like($search_term) . '%';

        $results = $this->wpdb->get_results($this->wpdb->prepare(
            "SELECT ID as id, display_name as name, user_login as login, user_email as email
             FROM {$this->wpdb->users}
             WHERE (display_name LIKE %s OR user_login LIKE %s OR user_email LIKE %s)
             AND user_status = 0
             ORDER BY display_name ASC
             LIMIT %d",
            $search_term,
            $search_term,
            $search_term,
            $limit
        ));

        return $results;
    }
    
    /**
     * Search counties for autocomplete
     */
    public function search_counties($search_term, $limit = 10) {
        $search_term = '%' . $this->wpdb->esc_like($search_term) . '%';
        
        $query = $this->wpdb->prepare("
            SELECT DISTINCT powiaty_uslugi as name
            FROM {$this->wpdb->prefix}geodir_gd_place_detail 
            WHERE powiaty_uslugi LIKE %s 
            AND powiaty_uslugi IS NOT NULL 
            AND powiaty_uslugi != ''
            ORDER BY powiaty_uslugi ASC 
            LIMIT %d
        ", $search_term, $limit);
        
        $results = $this->wpdb->get_results($query);
        
        // Process comma-separated values
        $counties = array();
        foreach ($results as $result) {
            $county_list = explode(',', $result->name);
            foreach ($county_list as $county) {
                $county = trim($county);
                if (!empty($county) && stripos($county, $search_term) !== false) {
                    $counties[] = (object) array('name' => $county);
                }
            }
        }
        
        // Remove duplicates and limit
        $unique_counties = array();
        $seen = array();
        foreach ($counties as $county) {
            if (!in_array($county->name, $seen)) {
                $unique_counties[] = $county;
                $seen[] = $county->name;
                if (count($unique_counties) >= $limit) {
                    break;
                }
            }
        }
        
        return $unique_counties;
    }
    
    /**
     * Search regions for autocomplete
     */
    public function search_regions($search_term, $limit = 10) {
        $search_term = '%' . $this->wpdb->esc_like($search_term) . '%';
        
        $query = $this->wpdb->prepare("
            SELECT DISTINCT region as name
            FROM {$this->wpdb->prefix}geodir_gd_place_detail 
            WHERE region LIKE %s 
            AND region IS NOT NULL 
            AND region != ''
            ORDER BY region ASC 
            LIMIT %d
        ", $search_term, $limit);
        
        return $this->wpdb->get_results($query);
    }
    
    /**
     * Get feed statistics
     */
    public function get_feed_statistics($feed_id) {
        $stats = array();

        // Get feed info
        $feed = $this->get_feed($feed_id);
        if (!$feed) {
            return false;
        }

        $stats['feed_name'] = $feed->name;
        $stats['feed_url'] = $feed->url;
        $stats['feed_status'] = $feed->status;
        $stats['created_at'] = $feed->created_at;

        // Total items
        $stats['total_items'] = $this->wpdb->get_var($this->wpdb->prepare(
            "SELECT COUNT(*) FROM {$this->items_table} WHERE feed_id = %d",
            $feed_id
        ));

        // Posts count (items with WordPress posts)
        $stats['posts_count'] = $this->wpdb->get_var($this->wpdb->prepare(
            "SELECT COUNT(*) FROM {$this->items_table} WHERE feed_id = %d AND post_id IS NOT NULL",
            $feed_id
        ));

        // Processed items
        $stats['processed_items'] = $this->wpdb->get_var($this->wpdb->prepare(
            "SELECT COUNT(*) FROM {$this->items_table} WHERE feed_id = %d AND processed = 1",
            $feed_id
        ));

        // Success rate calculation
        $total_items = intval($stats['total_items']);
        $processed_items = intval($stats['processed_items']);
        $stats['success_rate'] = $total_items > 0 ? round(($processed_items / $total_items) * 100, 1) : 0;

        // Update count (how many times feed was updated)
        $stats['update_count'] = $this->wpdb->get_var($this->wpdb->prepare(
            "SELECT COUNT(DISTINCT DATE(created_at)) FROM {$this->items_table} WHERE feed_id = %d",
            $feed_id
        ));

        // Recent items (last 24 hours)
        $stats['recent_items'] = $this->wpdb->get_var($this->wpdb->prepare(
            "SELECT COUNT(*) FROM {$this->items_table} WHERE feed_id = %d AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)",
            $feed_id
        ));

        // Last update
        $stats['last_updated'] = $this->wpdb->get_var($this->wpdb->prepare(
            "SELECT last_updated FROM {$this->feeds_table} WHERE id = %d",
            $feed_id
        ));

        return $stats;
    }
    
    /**
     * Get statistics for all feeds
     */
    public function get_statistics() {
        $stats = array(
            'total_feeds' => 0,
            'active_feeds' => 0,
            'total_items' => 0,
            'processed_items' => 0,
            'recent_items' => 0
        );

        try {
            // Total feeds
            $stats['total_feeds'] = intval($this->wpdb->get_var("SELECT COUNT(*) FROM {$this->feeds_table}"));

            // Active feeds
            $stats['active_feeds'] = intval($this->wpdb->get_var("SELECT COUNT(*) FROM {$this->feeds_table} WHERE status = 'active'"));

            // Total items
            $stats['total_items'] = intval($this->wpdb->get_var("SELECT COUNT(*) FROM {$this->items_table}"));

            // Processed items
            $stats['processed_items'] = intval($this->wpdb->get_var("SELECT COUNT(*) FROM {$this->items_table} WHERE processed = 1"));

            // Recent items (last 24 hours)
            $stats['recent_items'] = intval($this->wpdb->get_var("SELECT COUNT(*) FROM {$this->items_table} WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)"));

        } catch (Exception $e) {
            error_log('RSS Aggregator: Error getting statistics: ' . $e->getMessage());
        }

        return $stats;
    }
    
    /**
     * Check if item exists
     */
    public function item_exists($feed_id, $guid) {
        $existing = $this->wpdb->get_var($this->wpdb->prepare(
            "SELECT id FROM {$this->items_table} WHERE feed_id = %d AND guid = %s",
            $feed_id,
            $guid
        ));

        return !empty($existing);
    }

    /**
     * Save RSS item
     */
    public function save_item($data) {
        // Check if item already exists
        if ($this->item_exists($data['feed_id'], $data['guid'])) {
            return false; // Item already exists
        }

        $result = $this->wpdb->insert($this->items_table, $data);
        return $result ? $this->wpdb->insert_id : false;
    }
    
    /**
     * Update item
     */
    public function update_item($item_id, $data) {
        return $this->wpdb->update($this->items_table, $data, array('id' => $item_id));
    }

    /**
     * Update item post ID
     */
    public function update_item_post_id($item_id, $post_id) {
        return $this->wpdb->update(
            $this->items_table,
            array('post_id' => $post_id, 'processed' => 1),
            array('id' => $item_id)
        );
    }
    
    /**
     * Get unprocessed items
     */
    public function get_unprocessed_items($feed_id = null, $limit = 50) {
        $where = "WHERE processed = 0";
        $params = array();
        
        if ($feed_id) {
            $where .= " AND feed_id = %d";
            $params[] = $feed_id;
        }
        
        $query = "SELECT * FROM {$this->items_table} {$where} ORDER BY pub_date DESC LIMIT %d";
        $params[] = $limit;
        
        return $this->wpdb->get_results($this->wpdb->prepare($query, $params));
    }
    
    /**
     * Get feed items for statistics page
     */
    public function get_feed_items($feed_id, $limit = 20, $offset = 0) {
        $query = $this->wpdb->prepare("
            SELECT i.*, p.post_title, p.post_status 
            FROM {$this->items_table} i 
            LEFT JOIN {$this->wpdb->posts} p ON i.post_id = p.ID 
            WHERE i.feed_id = %d 
            ORDER BY i.created_at DESC 
            LIMIT %d OFFSET %d
        ", $feed_id, $limit, $offset);
        
        return $this->wpdb->get_results($query);
    }
    
    /**
     * Delete feed posts
     */
    public function delete_feed_posts($feed_id) {
        $feed_id = intval($feed_id);

        if (empty($feed_id)) {
            return array('success' => false, 'message' => 'Invalid feed ID');
        }

        // Get all items for this feed
        $items = $this->wpdb->get_results($this->wpdb->prepare(
            "SELECT id, post_id FROM {$this->items_table}
             WHERE feed_id = %d AND post_id IS NOT NULL",
            $feed_id
        ));

        $deleted_posts = 0;
        $deleted_items = 0;
        $errors = array();

        foreach ($items as $item) {
            if (!empty($item->post_id)) {
                // Delete WordPress post and all its metadata
                $result = wp_delete_post($item->post_id, true); // true = force delete, skip trash

                if ($result) {
                    $deleted_posts++;

                    // Delete post meta
                    $this->wpdb->delete($this->wpdb->postmeta, array('post_id' => $item->post_id));

                    // Delete from any taxonomy relationships
                    $this->wpdb->delete($this->wpdb->term_relationships, array('object_id' => $item->post_id));

                } else {
                    $errors[] = "Failed to delete post ID: {$item->post_id}";
                }
            }

            // Delete RSS item from our table
            $item_deleted = $this->wpdb->delete(
                $this->items_table,
                array('id' => $item->id),
                array('%d')
            );

            if ($item_deleted) {
                $deleted_items++;
            } else {
                $errors[] = "Failed to delete RSS item ID: {$item->id}";
            }
        }

        // Clean up any orphaned items (items without post_id)
        $orphaned_deleted = $this->wpdb->delete(
            $this->items_table,
            array('feed_id' => $feed_id),
            array('%d')
        );

        if ($orphaned_deleted) {
            $deleted_items += $orphaned_deleted;
        }

        return array(
            'success' => true,
            'deleted_posts' => $deleted_posts,
            'deleted_items' => $deleted_items,
            'errors' => $errors,
            'message' => sprintf(
                'Deleted %d posts and %d RSS items. %s',
                $deleted_posts,
                $deleted_items,
                !empty($errors) ? count($errors) . ' errors occurred.' : ''
            )
        );
    }

    /**
     * Get count of posts for a specific feed
     */
    public function get_feed_posts_count($feed_id) {
        return $this->wpdb->get_var($this->wpdb->prepare(
            "SELECT COUNT(*) FROM {$this->items_table} WHERE feed_id = %d AND post_id IS NOT NULL",
            $feed_id
        ));
    }

    /**
     * Get count of posts imported in the last 24 hours for a specific feed
     */
    public function get_feed_recent_posts_count($feed_id) {
        return $this->wpdb->get_var($this->wpdb->prepare(
            "SELECT COUNT(*) FROM {$this->items_table}
             WHERE feed_id = %d
             AND post_id IS NOT NULL
             AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)",
            $feed_id
        ));
    }

    /**
     * Get posts for a specific feed
     */
    public function get_feed_posts($feed_id, $limit = 20, $offset = 0) {
        $limit_clause = '';
        if ($limit > 0) {
            $limit_clause = $this->wpdb->prepare(" LIMIT %d OFFSET %d", $limit, $offset);
        }

        $query = $this->wpdb->prepare("
            SELECT i.*, p.post_title, p.post_status, p.post_date
            FROM {$this->items_table} i
            LEFT JOIN {$this->wpdb->posts} p ON i.post_id = p.ID
            WHERE i.feed_id = %d
            AND i.post_id IS NOT NULL
            ORDER BY i.created_at DESC
        ", $feed_id) . $limit_clause;

        error_log("RSS Aggregator: get_feed_posts query: " . $query);
        $results = $this->wpdb->get_results($query);
        error_log("RSS Aggregator: get_feed_posts returned " . count($results) . " results");

        return $results;
    }

    /**
     * Search GeoDirectory places for autocomplete
     */
    public function search_geodirectory_places($search_term, $limit = 10) {
        $search_term_clean = trim($search_term);
        $search_term_like = '%' . $this->wpdb->esc_like($search_term_clean) . '%';

        // Always search in WordPress posts for GeoDirectory places
        $results = $this->search_geodirectory_posts($search_term_like, $limit);

        // If no results from GeoDirectory, add sample places that match search
        if (empty($results)) {
            $sample_places = array(
                (object) array('id' => 1, 'name' => 'Warszawa', 'city' => 'Warszawa', 'region' => 'mazowieckie'),
                (object) array('id' => 2, 'name' => 'Kraków', 'city' => 'Kraków', 'region' => 'małopolskie'),
                (object) array('id' => 3, 'name' => 'Gdańsk', 'city' => 'Gdańsk', 'region' => 'pomorskie'),
                (object) array('id' => 4, 'name' => 'Wrocław', 'city' => 'Wrocław', 'region' => 'dolnośląskie'),
                (object) array('id' => 5, 'name' => 'Poznań', 'city' => 'Poznań', 'region' => 'wielkopolskie'),
                (object) array('id' => 6, 'name' => 'Łódź', 'city' => 'Łódź', 'region' => 'łódzkie'),
                (object) array('id' => 7, 'name' => 'Szczecin', 'city' => 'Szczecin', 'region' => 'zachodniopomorskie'),
                (object) array('id' => 8, 'name' => 'Bydgoszcz', 'city' => 'Bydgoszcz', 'region' => 'kujawsko-pomorskie'),
                (object) array('id' => 9, 'name' => 'Lublin', 'city' => 'Lublin', 'region' => 'lubelskie'),
                (object) array('id' => 10, 'name' => 'Foto-Nowak', 'city' => 'Dąbrowa Górnicza', 'region' => 'śląskie'),
            );

            $filtered_places = array();
            foreach ($sample_places as $place) {
                if (stripos($place->name, $search_term_clean) !== false ||
                    stripos($place->city, $search_term_clean) !== false) {
                    $filtered_places[] = $place;
                }
            }

            return array_slice($filtered_places, 0, $limit);
        }

        return $results;
    }

    /**
     * Search GeoDirectory places in posts table
     */
    private function search_geodirectory_posts($search_term, $limit = 10) {
        // Try different GeoDirectory post types
        $post_types = array('gd_place', 'geodir_place', 'place', 'listing');

        // First, check if any GeoDirectory posts exist
        $post_type_placeholders = implode(',', array_fill(0, count($post_types), '%s'));
        $check_sql = "SELECT COUNT(*) FROM {$this->wpdb->posts} WHERE post_type IN ({$post_type_placeholders}) AND post_status = 'publish'";
        $check_params = $post_types;
        $gd_posts_count = $this->wpdb->get_var($this->wpdb->prepare($check_sql, $check_params));

        if ($gd_posts_count > 0) {
            // Build the main query
            $post_type_conditions = array();
            foreach ($post_types as $type) {
                $post_type_conditions[] = $this->wpdb->prepare("p.post_type = %s", $type);
            }
            $post_type_sql = '(' . implode(' OR ', $post_type_conditions) . ')';

            $sql = "SELECT p.ID as id, p.post_title as name,
                           COALESCE(pm1.meta_value, '') as city,
                           COALESCE(pm2.meta_value, '') as region
                    FROM {$this->wpdb->posts} p
                    LEFT JOIN {$this->wpdb->postmeta} pm1 ON p.ID = pm1.post_id AND pm1.meta_key IN ('geodir_city', 'city', '_city')
                    LEFT JOIN {$this->wpdb->postmeta} pm2 ON p.ID = pm2.post_id AND pm2.meta_key IN ('geodir_region', 'region', '_region', 'geodir_state', 'state', '_state')
                    WHERE {$post_type_sql}
                    AND p.post_title LIKE %s
                    AND p.post_status = 'publish'
                    ORDER BY p.post_title ASC
                    LIMIT %d";

            $results = $this->wpdb->get_results($this->wpdb->prepare($sql, $search_term, $limit));

            return $results ? $results : array();
        }

        return array();
    }

    /**
     * Get single item by ID
     */
    public function get_item($item_id) {
        return $this->wpdb->get_row($this->wpdb->prepare(
            "SELECT * FROM {$this->items_table} WHERE id = %d",
            $item_id
        ));
    }

    /**
     * Get items for a feed (alias for compatibility)
     */
    public function get_items($feed_id = 0, $args = array()) {
        $defaults = array(
            'limit' => 50,
            'offset' => 0,
            'processed' => null
        );

        $args = array_merge($defaults, $args);

        $where = "WHERE 1=1";
        $params = array();

        if ($feed_id > 0) {
            $where .= " AND feed_id = %d";
            $params[] = $feed_id;
        }

        if ($args['processed'] !== null) {
            $where .= " AND processed = %d";
            $params[] = $args['processed'];
        }

        $query = "SELECT * FROM {$this->items_table} {$where} ORDER BY created_at DESC LIMIT %d OFFSET %d";
        $params[] = $args['limit'];
        $params[] = $args['offset'];

        if (!empty($params)) {
            return $this->wpdb->get_results($this->wpdb->prepare($query, $params));
        } else {
            return $this->wpdb->get_results($query);
        }
    }
}
