<?php
/**
 * RSS Admin class
 *
 * @package RSS_Aggregator
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * RSS Admin class
 */
class RSS_Admin {
    
    /**
     * Plugin instance
     */
    private static $instance = null;
    
    /**
     * Database handler
     */
    private $database;
    
    /**
     * Current page
     */
    private $current_page = 'feeds';
    
    /**
     * Get plugin instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        // Initialize database immediately
        $this->database = new RSS_Database();

        // Only initialize admin functionality in admin area
        if (is_admin()) {
            $this->init();
        }
    }
    
    /**
     * Initialize admin functionality
     */
    private function init() {
        // Admin menu
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // Admin init
        add_action('admin_init', array($this, 'admin_init'));
        
        // Handle form submissions
        add_action('admin_post_rss_aggregator_save_feed', array($this, 'handle_save_feed'));
        add_action('admin_post_rss_aggregator_delete_feed', array($this, 'handle_delete_feed'));
        add_action('admin_post_rss_aggregator_save_settings', array($this, 'handle_save_settings'));
        
        // AJAX handlers
        add_action('wp_ajax_rss_aggregator_test_feed', array($this, 'ajax_test_feed'));
        add_action('wp_ajax_rss_aggregator_force_update', array($this, 'ajax_force_update'));
        add_action('wp_ajax_rss_aggregator_get_places', array($this, 'ajax_get_places'));
        add_action('wp_ajax_rss_aggregator_search_places', array($this, 'ajax_search_places'));
        add_action('wp_ajax_rss_aggregator_search_users', array($this, 'ajax_search_users'));
        add_action('wp_ajax_rss_aggregator_search_counties', array($this, 'ajax_search_counties'));
        add_action('wp_ajax_rss_aggregator_search_regions', array($this, 'ajax_search_regions'));
        add_action('wp_ajax_rss_aggregator_delete_feed_posts', array($this, 'ajax_delete_feed_posts'));
        add_action('wp_ajax_rss_aggregator_manual_cleanup', array($this, 'ajax_manual_cleanup'));
        add_action('wp_ajax_rss_aggregator_reset_cron', array($this, 'ajax_reset_cron'));
        add_action('wp_ajax_rss_aggregator_trigger_cron', array($this, 'ajax_trigger_cron'));
        add_action('wp_ajax_rss_aggregator_test_author', array($this, 'ajax_test_author'));
        add_action('wp_ajax_rss_aggregator_test_cron', array($this, 'ajax_test_cron'));
        add_action('wp_ajax_rss_aggregator_refresh_post', array($this, 'ajax_refresh_post'));
        add_action('wp_ajax_rss_aggregator_refresh_all_posts', array($this, 'ajax_refresh_all_posts'));

    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        error_log('RSS Aggregator: Adding admin menu');
        $capability = 'manage_options';
        
        // Main menu page
        add_menu_page(
            __('RSS Aggregator', 'rss-aggregator'),
            __('RSS Aggregator', 'rss-aggregator'),
            $capability,
            'rss-aggregator',
            array($this, 'display_feeds_page'),
            'dashicons-rss',
            30
        );
        
        // Submenus
        add_submenu_page(
            'rss-aggregator',
            __('RSS Feeds', 'rss-aggregator'),
            __('Feeds', 'rss-aggregator'),
            $capability,
            'rss-aggregator',
            array($this, 'display_feeds_page')
        );
        
        add_submenu_page(
            'rss-aggregator',
            __('Add New Feed', 'rss-aggregator'),
            __('Add New', 'rss-aggregator'),
            $capability,
            'rss-aggregator-add',
            array($this, 'display_add_feed_page')
        );
        
        add_submenu_page(
            'rss-aggregator',
            __('Settings', 'rss-aggregator'),
            __('Settings', 'rss-aggregator'),
            $capability,
            'rss-aggregator-settings',
            array($this, 'display_settings_page')
        );
        
        // Hidden statistics page (accessed via direct link)
        add_submenu_page(
            null, // Hidden from menu
            __('Feed Statistics', 'rss-aggregator'),
            __('Statistics', 'rss-aggregator'),
            $capability,
            'rss-aggregator-statistics',
            array($this, 'display_statistics_page')
        );

        add_submenu_page(
            'rss-aggregator',
            __('Status', 'rss-aggregator'),
            __('Status', 'rss-aggregator'),
            $capability,
            'rss-aggregator-status',
            array($this, 'display_status_page')
        );

        // Debug page (only for administrators)
        if (current_user_can('administrator')) {
            add_submenu_page(
                'rss-aggregator',
                __('Debug', 'rss-aggregator'),
                __('Debug', 'rss-aggregator'),
                'administrator',
                'rss-aggregator-debug',
                array($this, 'display_debug_page')
            );
        }
    }
    
    /**
     * Admin init
     */
    public function admin_init() {
        // Register settings
        $this->register_settings();
        
        // Enqueue scripts and styles
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
    }
    
    /**
     * Register settings
     */
    private function register_settings() {
        register_setting('rss_aggregator_settings', 'rss_aggregator_default_frequency');
        register_setting('rss_aggregator_settings', 'rss_aggregator_max_items_per_feed');
        register_setting('rss_aggregator_settings', 'rss_aggregator_default_initial_count');
        register_setting('rss_aggregator_settings', 'rss_aggregator_enable_thumbnails');
        register_setting('rss_aggregator_settings', 'rss_aggregator_enable_advanced_thumbnails');
        register_setting('rss_aggregator_settings', 'rss_aggregator_enable_advanced_rss_search');
        register_setting('rss_aggregator_settings', 'rss_aggregator_enable_buddyboss');
        register_setting('rss_aggregator_settings', 'rss_aggregator_enable_geodirectory');
        register_setting('rss_aggregator_settings', 'rss_aggregator_post_status');
        register_setting('rss_aggregator_settings', 'rss_aggregator_post_author');
        register_setting('rss_aggregator_settings', 'rss_aggregator_post_date_source');
        register_setting('rss_aggregator_settings', 'rss_aggregator_cleanup_days');
    }
    
    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook) {
        if (strpos($hook, 'rss-aggregator') === false) {
            return;
        }
        
        wp_enqueue_script(
            'rss-aggregator-admin',
            RSS_AGGREGATOR_PLUGIN_URL . 'admin/js/admin.js',
            array('jquery', 'wp-util'),
            RSS_AGGREGATOR_VERSION,
            true
        );
        
        wp_enqueue_style(
            'rss-aggregator-admin',
            RSS_AGGREGATOR_PLUGIN_URL . 'admin/css/admin.css',
            array(),
            RSS_AGGREGATOR_VERSION
        );
        
        wp_localize_script('rss-aggregator-admin', 'rssAggregatorAdmin', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('rss_aggregator_admin'),
            'strings' => array(
                'testing' => __('Testing feed...', 'rss-aggregator'),
                'updating' => __('Updating feed...', 'rss-aggregator'),
                'success' => __('Success', 'rss-aggregator'),
                'error' => __('Error', 'rss-aggregator'),
                'confirmDelete' => __('Are you sure you want to delete this feed?', 'rss-aggregator'),
                'loadingPlaces' => __('Loading places...', 'rss-aggregator')
            )
        ));
    }
    
    /**
     * Display feeds page
     */
    public function display_feeds_page() {
        $this->current_page = 'feeds';
        
        // Handle bulk actions
        if (isset($_POST['action']) && $_POST['action'] === 'bulk_delete') {
            $this->handle_bulk_delete();
        }
        
        // Get feeds
        $feeds = $this->database->get_feeds();
        $statistics = $this->database->get_statistics();
        $database = $this->database; // Make database available to the template

        include RSS_AGGREGATOR_PLUGIN_DIR . 'admin/partials/feeds-list.php';
    }
    
    /**
     * Display add/edit feed page
     */
    public function display_add_feed_page() {
        $this->current_page = 'add';
        
        $feed = null;
        $edit_mode = false;
        
        // Check if editing
        if (isset($_GET['edit']) && !empty($_GET['edit'])) {
            $feed_id = intval($_GET['edit']);
            $feed = $this->database->get_feed($feed_id);
            $edit_mode = true;
        }
        
        // Get GeoDirectory places and counties
        $integrations = new RSS_Integrations();
        $places = $integrations->get_geodirectory_places();
        $counties = $integrations->get_geodirectory_counties();
        
        // Get available frequencies
        $cron = new RSS_Cron();
        $frequencies = $cron->get_available_frequencies();
        
        include RSS_AGGREGATOR_PLUGIN_DIR . 'admin/partials/feed-form.php';
    }
    
    /**
     * Display settings page
     */
    public function display_settings_page() {
        $this->current_page = 'settings';
        
        // Get available post statuses
        $post_statuses = get_post_statuses();
        
        // Get users for author selection
        $users = get_users(array('role__in' => array('administrator', 'editor', 'author')));
        
        include RSS_AGGREGATOR_PLUGIN_DIR . 'admin/partials/settings.php';
    }

    /**
     * Display statistics page
     */
    public function display_statistics_page() {
        include RSS_AGGREGATOR_PLUGIN_DIR . 'admin/partials/feed-statistics.php';
    }

    /**
     * Display debug page
     */
    public function display_debug_page() {
        $this->current_page = 'debug';

        // Handle direct cron test
        if (isset($_GET['test_cron']) && $_GET['test_cron'] == '1' && current_user_can('administrator')) {
            $this->handle_direct_cron_test();
        }

        include RSS_AGGREGATOR_PLUGIN_DIR . 'admin/partials/debug-page.php';
    }

    /**
     * Handle direct cron test
     */
    private function handle_direct_cron_test() {
        echo '<div class="notice notice-info"><p><strong>Testing Cron...</strong></p></div>';

        try {
            // Get RSS_Cron instance
            $cron = new RSS_Cron();

            // Get feeds due for update
            $database = new RSS_Database();
            $feeds = $database->get_feeds_due_for_update();

            echo '<div class="notice notice-info"><p>Found ' . count($feeds) . ' feeds due for update.</p></div>';

            if (count($feeds) > 0) {
                // Run cron manually
                $cron->check_feeds_cron();
                echo '<div class="notice notice-success"><p><strong>✅ Cron executed successfully!</strong></p></div>';
            } else {
                echo '<div class="notice notice-warning"><p>No feeds need updating at this time.</p></div>';
            }

        } catch (Exception $e) {
            echo '<div class="notice notice-error"><p><strong>❌ Cron test failed:</strong> ' . $e->getMessage() . '</p></div>';
        }
    }

    /**
     * Display status page
     */
    public function display_status_page() {
        $this->current_page = 'status';
        
        // Get cron status
        $cron = new RSS_Cron();
        $cron_status = $cron->get_cron_status();
        $time_until_next = $cron->get_time_until_next_run();
        
        // Get statistics
        $statistics = $this->database->get_statistics();
        
        // Get recent activity
        $recent_items = $this->database->get_items(0, array('limit' => 10));
        
        include RSS_AGGREGATOR_PLUGIN_DIR . 'admin/partials/status.php';
    }
    
    /**
     * Handle save feed
     */
    public function handle_save_feed() {
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'rss-aggregator'));
        }
        
        check_admin_referer('rss_aggregator_save_feed');
        
        $feed_data = array(
            'name' => sanitize_text_field($_POST['name']),
            'url' => esc_url_raw($_POST['url']),
            'geodirectory_place_id' => !empty($_POST['geodirectory_place_id']) ? intval($_POST['geodirectory_place_id']) : null,
            'geodirectory_place_name' => sanitize_text_field($_POST['geodirectory_place_name']),
            'assigned_user_id' => !empty($_POST['assigned_user_id']) ? intval($_POST['assigned_user_id']) : null,
            'assigned_user_name' => sanitize_text_field($_POST['assigned_user_name']),
            'county' => sanitize_text_field($_POST['county']),
            'region' => sanitize_text_field($_POST['region']),
            'update_frequency' => sanitize_text_field($_POST['update_frequency']),
            'initial_import_count' => !empty($_POST['initial_import_count']) ? max(1, min(100, intval($_POST['initial_import_count']))) : 10,
            'status' => sanitize_text_field($_POST['status'])
        );

        // Debug logging
        error_log("RSS Aggregator: Form data received: " . print_r($feed_data, true));
        
        // If editing, add ID
        if (!empty($_POST['feed_id'])) {
            $feed_data['id'] = intval($_POST['feed_id']);
        }
        
        $result = $this->database->save_feed($feed_data);
        
        if ($result) {
            // Update cron schedule
            $cron = new RSS_Cron();
            $cron->schedule_feed_update($result, $feed_data['update_frequency']);
            
            $redirect_url = add_query_arg(array(
                'page' => 'rss-aggregator',
                'message' => 'saved'
            ), admin_url('admin.php'));
        } else {
            $redirect_url = add_query_arg(array(
                'page' => 'rss-aggregator-add',
                'message' => 'error'
            ), admin_url('admin.php'));
        }
        
        wp_redirect($redirect_url);
        exit;
    }
    
    /**
     * Handle delete feed
     */
    public function handle_delete_feed() {
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'rss-aggregator'));
        }
        
        check_admin_referer('rss_aggregator_delete_feed');
        
        $feed_id = intval($_POST['feed_id']);
        
        if ($feed_id) {
            // Remove cron schedule
            $cron = new RSS_Cron();
            $cron->unschedule_feed_update($feed_id);
            
            // Delete feed
            $result = $this->database->delete_feed($feed_id);
            
            $message = $result ? 'deleted' : 'error';
        } else {
            $message = 'error';
        }
        
        $redirect_url = add_query_arg(array(
            'page' => 'rss-aggregator',
            'message' => $message
        ), admin_url('admin.php'));
        
        wp_redirect($redirect_url);
        exit;
    }
    
    /**
     * Handle bulk delete
     */
    private function handle_bulk_delete() {
        if (!current_user_can('manage_options')) {
            return;
        }
        
        if (empty($_POST['feeds']) || !is_array($_POST['feeds'])) {
            return;
        }
        
        check_admin_referer('bulk-feeds');
        
        $cron = new RSS_Cron();
        $deleted_count = 0;
        
        foreach ($_POST['feeds'] as $feed_id) {
            $feed_id = intval($feed_id);
            if ($feed_id) {
                $cron->unschedule_feed_update($feed_id);
                if ($this->database->delete_feed($feed_id)) {
                    $deleted_count++;
                }
            }
        }
        
        $message = $deleted_count > 0 ? 'bulk_deleted' : 'error';
        
        $redirect_url = add_query_arg(array(
            'page' => 'rss-aggregator',
            'message' => $message,
            'deleted' => $deleted_count
        ), admin_url('admin.php'));
        
        wp_redirect($redirect_url);
        exit;
    }
    
    /**
     * Handle save settings
     */
    public function handle_save_settings() {
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'rss-aggregator'));
        }
        
        check_admin_referer('rss_aggregator_settings');
        
        // Save settings
        $settings = array(
            'rss_aggregator_default_frequency',
            'rss_aggregator_max_items_per_feed',
            'rss_aggregator_default_initial_count',
            'rss_aggregator_enable_thumbnails',
            'rss_aggregator_enable_advanced_thumbnails',
            'rss_aggregator_enable_advanced_rss_search',
            'rss_aggregator_enable_buddyboss',
            'rss_aggregator_enable_geodirectory',
            'rss_aggregator_post_status',
            'rss_aggregator_post_author',
            'rss_aggregator_post_date_source',
            'rss_aggregator_cleanup_days',
            'rss_aggregator_language'
        );
        
        foreach ($settings as $setting) {
            if (isset($_POST[$setting])) {
                update_option($setting, sanitize_text_field($_POST[$setting]));
            }
        }
        
        $redirect_url = add_query_arg(array(
            'page' => 'rss-aggregator-settings',
            'message' => 'settings_saved'
        ), admin_url('admin.php'));
        
        wp_redirect($redirect_url);
        exit;
    }
    
    /**
     * AJAX test feed
     */
    public function ajax_test_feed() {
        error_log('RSS Aggregator: ajax_test_feed called');

        if (!current_user_can('administrator')) {
            error_log('RSS Aggregator: ajax_test_feed - insufficient permissions');
            wp_send_json_error(__('Insufficient permissions', 'rss-aggregator'));
            return;
        }

        error_log('RSS Aggregator: ajax_test_feed - permissions OK');

        try {
            check_ajax_referer('rss_aggregator_admin', 'nonce');
        } catch (Exception $e) {
            wp_send_json_error(__('Security check failed', 'rss-aggregator'));
            return;
        }

        $feed_url = sanitize_url($_POST['feed_url'] ?? '');

        if (empty($feed_url)) {
            wp_send_json_error(__('Feed URL is required', 'rss-aggregator'));
            return;
        }

        // Test the feed using RSS_Fetcher
        $fetcher = new RSS_Fetcher();
        $parser = new RSS_Parser();

        // First, try to fetch the feed
        $feed_content = $fetcher->fetch_feed($feed_url);

        error_log('RSS Aggregator: Feed content result: ' . ($feed_content === false ? 'FALSE' : 'SUCCESS'));
        error_log('RSS Aggregator: Feed content length: ' . (is_string($feed_content) ? strlen($feed_content) : 'NOT STRING'));

        if ($feed_content === false) {
            wp_send_json_error(__('Nie udało się pobrać zawartości kanału', 'rss-aggregator'));
            return;
        }

        if (empty($feed_content)) {
            wp_send_json_error(__('Pobrana zawartość kanału jest pusta', 'rss-aggregator'));
            return;
        }

        error_log('RSS Aggregator: About to call parse_feed_info');

        // Try to parse the feed info
        $parsed_feed = $parser->parse_feed_info($feed_content);

        error_log('RSS Aggregator: parse_feed_info result: ' . ($parsed_feed === false ? 'FALSE' : 'SUCCESS'));

        if ($parsed_feed === false) {
            wp_send_json_error(__('Failed to parse feed content. The feed may be malformed.', 'rss-aggregator'));
            return;
        }

        // Check if we have basic feed info
        if (empty($parsed_feed['title']) || $parsed_feed['title'] === 'Unknown') {
            wp_send_json_error(__('Nie znaleziono elementów w kanale lub nieprawidłowy format RSS', 'rss-aggregator'));
            return;
        }

        // Extract feed information
        $result = array(
            'title' => $parsed_feed['title'] ?? 'Unknown',
            'description' => $parsed_feed['description'] ?? 'No description',
            'link' => $parsed_feed['link'] ?? '',
            'last_build_date' => $parsed_feed['last_build_date'] ?? 'Unknown',
            'item_count' => count($parsed_feed['items'] ?? []),
            'sample_items' => array()
        );

        // Get first 3 items as samples
        if (!empty($parsed_feed['items'])) {
            $sample_count = min(3, count($parsed_feed['items']));
            for ($i = 0; $i < $sample_count; $i++) {
                $item = $parsed_feed['items'][$i];
                $result['sample_items'][] = array(
                    'title' => $item['title'] ?? 'No title',
                    'pub_date' => $item['pub_date'] ?? 'Unknown date',
                    'link' => $item['link'] ?? ''
                );
            }
        }

        wp_send_json_success($result);
    }
    
    /**
     * AJAX force update
     */
    public function ajax_force_update() {
        check_ajax_referer('rss_aggregator_admin', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'rss-aggregator'));
        }
        
        $feed_id = intval($_POST['feed_id']);
        
        if (empty($feed_id)) {
            wp_send_json_error(__('Feed ID is required', 'rss-aggregator'));
        }
        
        $aggregator = RSS_Aggregator::get_instance();
        $result = $aggregator->update_single_feed($feed_id);
        
        if ($result !== false) {
            wp_send_json_success(array(
                'message' => sprintf(__('Processed %d items', 'rss-aggregator'), $result),
                'items_processed' => $result
            ));
        } else {
            wp_send_json_error(__('Failed to update feed', 'rss-aggregator'));
        }
    }
    
    /**
     * AJAX get places
     */
    public function ajax_get_places() {
        check_ajax_referer('rss_aggregator_admin', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'rss-aggregator'));
        }
        
        $post_type = sanitize_text_field($_POST['post_type']);
        
        $integrations = new RSS_Integrations();
        $places = $integrations->get_geodirectory_places($post_type);
        
        wp_send_json_success($places);
    }
    
    /**
     * Display admin notices
     */
    public function display_admin_notices() {
        if (!isset($_GET['message'])) {
            return;
        }
        
        $message = $_GET['message'];
        $class = 'notice-success';
        $text = '';
        
        switch ($message) {
            case 'saved':
                $text = __('Feed saved successfully.', 'rss-aggregator');
                break;
            case 'deleted':
                $text = __('Feed deleted successfully.', 'rss-aggregator');
                break;
            case 'bulk_deleted':
                $deleted = isset($_GET['deleted']) ? intval($_GET['deleted']) : 0;
                $text = sprintf(__('%d feeds deleted successfully.', 'rss-aggregator'), $deleted);
                break;
            case 'settings_saved':
                $text = __('Settings saved successfully.', 'rss-aggregator');
                break;
            case 'error':
                $text = __('An error occurred. Please try again.', 'rss-aggregator');
                $class = 'notice-error';
                break;
        }
        
        if (!empty($text)) {
            echo '<div class="notice ' . $class . ' is-dismissible"><p>' . $text . '</p></div>';
        }
    }
    
    /**
     * AJAX manual cleanup
     */
    public function ajax_manual_cleanup() {
        check_ajax_referer('rss_aggregator_admin', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'rss-aggregator'));
        }

        $days_to_keep = get_option('rss_aggregator_cleanup_days', 30);
        $deleted_count = $this->database->clean_old_items($days_to_keep);

        wp_send_json_success(array(
            'message' => sprintf(__('Cleaned up %d old items.', 'rss-aggregator'), $deleted_count)
        ));
    }

    /**
     * AJAX reset cron
     */
    public function ajax_reset_cron() {
        check_ajax_referer('rss_aggregator_admin', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'rss-aggregator'));
        }

        $cron = new RSS_Cron();
        $cron->reset_all_schedules();

        wp_send_json_success(array(
            'message' => __('All cron schedules have been reset successfully.', 'rss-aggregator')
        ));
    }

    /**
     * AJAX trigger cron
     */
    public function ajax_trigger_cron() {
        check_ajax_referer('rss_aggregator_admin', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'rss-aggregator'));
        }

        $cron = new RSS_Cron();
        $result = $cron->trigger_manual_cron();

        if ($result) {
            wp_send_json_success(array(
                'message' => __('Cron job triggered successfully.', 'rss-aggregator')
            ));
        } else {
            wp_send_json_error(__('Failed to trigger cron job.', 'rss-aggregator'));
        }
    }

    /**
     * Get current page
     */
    public function get_current_page() {
        return $this->current_page;
    }

    /**
     * Main admin page callback (redirects to feeds page)
     */
    public function display_admin_page() {
        $this->display_feeds_page();
    }

    /**
     * AJAX handler for searching places
     */
    public function ajax_search_places() {
        try {
            check_ajax_referer('rss_aggregator_admin', 'nonce');
        } catch (Exception $e) {
            wp_send_json_error(__('Security check failed', 'rss-aggregator'));
            return;
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'rss-aggregator'));
            return;
        }

        $search_term = sanitize_text_field($_POST['query'] ?? $_POST['search'] ?? '');

        if (empty($search_term)) {
            wp_send_json_error(__('Search term is required', 'rss-aggregator'));
            return;
        }

        $places = $this->database->search_geodirectory_places($search_term, 10);

        wp_send_json_success($places);
    }

    /**
     * AJAX handler for searching users
     */
    public function ajax_search_users() {
        check_ajax_referer('rss_aggregator_admin', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'rss-aggregator'));
        }

        $search_term = sanitize_text_field($_POST['query'] ?? $_POST['search'] ?? '');

        if (empty($search_term)) {
            wp_send_json_error(__('Search term is required', 'rss-aggregator'));
        }

        $users = $this->database->search_users($search_term, 10);

        wp_send_json_success($users);
    }

    /**
     * AJAX handler for searching counties
     */
    public function ajax_search_counties() {
        try {
            check_ajax_referer('rss_aggregator_admin', 'nonce');
        } catch (Exception $e) {
            wp_send_json_error(__('Security check failed', 'rss-aggregator'));
            return;
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'rss-aggregator'));
            return;
        }

        $search_term = sanitize_text_field($_POST['search'] ?? '');

        if (empty($search_term)) {
            wp_send_json_error(__('Search term is required', 'rss-aggregator'));
            return;
        }

        $counties = $this->database->search_counties($search_term, 10);

        wp_send_json_success($counties);
    }

    /**
     * AJAX handler for searching regions/voivodeships
     */
    public function ajax_search_regions() {
        try {
            check_ajax_referer('rss_aggregator_admin', 'nonce');
        } catch (Exception $e) {
            wp_send_json_error(__('Security check failed', 'rss-aggregator'));
            return;
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'rss-aggregator'));
            return;
        }

        $search_term = sanitize_text_field($_POST['search'] ?? '');

        if (empty($search_term)) {
            wp_send_json_error(__('Search term is required', 'rss-aggregator'));
            return;
        }

        $regions = $this->database->search_regions($search_term, 10);

        wp_send_json_success($regions);
    }

    /**
     * AJAX handler for deleting feed posts
     */
    public function ajax_delete_feed_posts() {
        check_ajax_referer('rss_aggregator_admin', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'rss-aggregator'));
        }

        $feed_id = intval($_POST['feed_id']);

        if (empty($feed_id)) {
            wp_send_json_error(__('Feed ID is required', 'rss-aggregator'));
        }

        // Get posts count before deletion
        $posts_count = $this->database->get_feed_posts_count($feed_id);

        if ($posts_count == 0) {
            wp_send_json_error(__('No posts found for this feed', 'rss-aggregator'));
        }

        // Confirm deletion
        $result = $this->database->delete_feed_posts($feed_id);

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * AJAX handler for testing author determination
     */
    public function ajax_test_author() {
        if (!current_user_can('administrator')) {
            wp_send_json_error(__('Insufficient permissions', 'rss-aggregator'));
            return;
        }

        $feed_id = intval($_POST['feed_id'] ?? 0);

        if (empty($feed_id)) {
            wp_send_json_error(__('Feed ID is required', 'rss-aggregator'));
            return;
        }

        // Get feed
        $feed = $this->database->get_feed($feed_id);
        if (!$feed) {
            wp_send_json_error(__('Feed not found', 'rss-aggregator'));
            return;
        }

        // Debug: Log feed data
        error_log("RSS Aggregator: AJAX Test Author - Feed data: " . print_r($feed, true));

        // Test author determination
        $rss_aggregator = RSS_Aggregator::get_instance();
        $reflection = new ReflectionClass($rss_aggregator);
        $method = $reflection->getMethod('determine_post_author');
        $method->setAccessible(true);
        $author_id = $method->invoke($rss_aggregator, $feed);

        $author = get_user_by('id', $author_id);

        $result = array(
            'feed_id' => $feed_id,
            'feed_name' => $feed->name,
            'geodirectory_place_id' => $feed->geodirectory_place_id,
            'assigned_user_id' => $feed->assigned_user_id,
            'determined_author_id' => $author_id,
            'determined_author_name' => $author ? $author->display_name : 'Unknown',
            'default_author_setting' => get_option('rss_aggregator_post_author', 1)
        );

        wp_send_json_success($result);
    }

    /**
     * AJAX handler for testing cron
     */
    public function ajax_test_cron() {
        if (!current_user_can('administrator')) {
            wp_send_json_error(__('Insufficient permissions', 'rss-aggregator'));
            return;
        }

        try {
            check_ajax_referer('rss_aggregator_admin', 'nonce');
        } catch (Exception $e) {
            wp_send_json_error(__('Security check failed', 'rss-aggregator'));
            return;
        }

        // Get RSS_Cron instance
        $cron = new RSS_Cron();

        // Get feeds due for update
        $database = new RSS_Database();
        $feeds = $database->get_feeds_due_for_update();

        $message = sprintf(__('Found %d feeds due for update. ', 'rss-aggregator'), count($feeds));

        if (count($feeds) > 0) {
            // Run cron manually
            $cron->check_feeds_cron();
            $message .= __('Cron executed successfully.', 'rss-aggregator');
        } else {
            $message .= __('No feeds need updating at this time.', 'rss-aggregator');
        }

        wp_send_json_success(array('message' => $message));
    }

    /**
     * AJAX handler for refreshing a post
     */
    public function ajax_refresh_post() {
        if (!current_user_can('administrator')) {
            wp_send_json_error(__('Insufficient permissions', 'rss-aggregator'));
            return;
        }

        try {
            check_ajax_referer('rss_aggregator_admin', 'nonce');
        } catch (Exception $e) {
            wp_send_json_error(__('Security check failed', 'rss-aggregator'));
            return;
        }

        $item_id = intval($_POST['item_id'] ?? 0);
        $post_id = intval($_POST['post_id'] ?? 0);

        if (!$item_id || !$post_id) {
            wp_send_json_error(__('Invalid item or post ID', 'rss-aggregator'));
            return;
        }

        // Get item data
        $database = new RSS_Database();
        $item = $database->get_item($item_id);

        if (!$item) {
            wp_send_json_error(__('Item not found', 'rss-aggregator'));
            return;
        }

        // Get feed data
        $feed = $database->get_feed($item->feed_id);

        if (!$feed) {
            wp_send_json_error(__('Feed not found', 'rss-aggregator'));
            return;
        }

        try {
            // Get RSS_Aggregator instance
            $aggregator = RSS_Aggregator::get_instance();

            // Prepare item data array
            $item_data = array(
                'title' => $item->title,
                'content' => $item->content,
                'excerpt' => $item->excerpt,
                'url' => $item->url,
                'guid' => $item->guid,
                'pub_date' => $item->pub_date,
                'author' => $item->author,
                'categories' => $item->categories
            );

            // Update post content
            $post_data = array(
                'ID' => $post_id,
                'post_title' => $item->title,
                'post_content' => $item->content,
                'post_excerpt' => $item->excerpt
            );

            $result = wp_update_post($post_data);

            if (is_wp_error($result)) {
                wp_send_json_error(__('Failed to update post: ', 'rss-aggregator') . $result->get_error_message());
                return;
            }

            // Try to refresh thumbnail
            $thumbnail_extractor = $aggregator->get_thumbnail_extractor();
            // Add content to item_data for thumbnail extraction
            if (!empty($item->content)) {
                $item_data['content'] = $item->content;
            }
            $thumbnail_url = $thumbnail_extractor->extract_thumbnail($item_data);

            if ($thumbnail_url) {
                // Remove existing thumbnail
                delete_post_thumbnail($post_id);

                // Set new thumbnail
                $attachment_id = $aggregator->download_and_attach_image($thumbnail_url, $post_id);
                if ($attachment_id) {
                    set_post_thumbnail($post_id, $attachment_id);
                }
            }

            wp_send_json_success(__('Post refreshed successfully', 'rss-aggregator'));

        } catch (Exception $e) {
            wp_send_json_error(__('Error refreshing post: ', 'rss-aggregator') . $e->getMessage());
        }
    }

    /**
     * AJAX handler for refreshing all posts for a feed
     */
    public function ajax_refresh_all_posts() {
        if (!current_user_can('administrator')) {
            wp_send_json_error(__('Insufficient permissions', 'rss-aggregator'));
            return;
        }

        if (!wp_verify_nonce($_POST['nonce'], 'rss_aggregator_admin')) {
            wp_send_json_error(__('Invalid nonce', 'rss-aggregator'));
            return;
        }

        $feed_id = intval($_POST['feed_id']);
        if (empty($feed_id)) {
            wp_send_json_error(__('Invalid feed ID', 'rss-aggregator'));
            return;
        }

        try {
            // Increase time and memory limits for bulk operations
            set_time_limit(300); // 5 minutes
            ini_set('memory_limit', '512M');

            $database = new RSS_Database();

            error_log("RSS Aggregator: Refresh all posts for feed ID: " . $feed_id);

            // Get posts for this feed (limit to 10 to prevent timeouts)
            $posts = $database->get_feed_posts($feed_id, 10); // Limit to 10 posts

            error_log("RSS Aggregator: Found " . count($posts) . " posts for feed " . $feed_id);

            if (empty($posts)) {
                error_log("RSS Aggregator: No posts found for feed " . $feed_id);
                wp_send_json_error(__('No posts found for this feed', 'rss-aggregator'));
                return;
            }

            $refreshed_count = 0;
            $errors = array();
            $thumbnail_extractor = new RSS_Thumbnail_Extractor();
            $aggregator = new RSS_Aggregator();

            foreach ($posts as $post) {
                error_log("RSS Aggregator: Starting to process post");

                if (empty($post->post_id)) {
                    error_log("RSS Aggregator: Skipping post without post_id");
                    continue; // Skip items without WordPress posts
                }

                try {
                    // Check what fields are available
                    $post_array = (array)$post;
                    error_log("RSS Aggregator: Post object keys: " . implode(', ', array_keys($post_array)));

                    // Try to get item ID - could be 'id' or something else
                    $item_id = isset($post->id) ? $post->id : (isset($post->item_id) ? $post->item_id : null);

                    if (!$item_id) {
                        error_log("RSS Aggregator: No item ID found in post object");
                        continue;
                    }

                    error_log("RSS Aggregator: Processing post ID {$post->post_id}, item ID {$item_id}");

                    // Get full item data using get_item() (same as single refresh)
                    $item = $database->get_item($item_id);
                    if (!$item) {
                        error_log("RSS Aggregator: Could not get item data for item ID {$item_id}");
                        continue;
                    }

                    error_log("RSS Aggregator: Item object keys: " . implode(', ', array_keys((array)$item)));

                    // Get original RSS item data (same as single refresh)
                    $item_data = array(
                        'title' => $item->title,
                        'description' => $item->description,
                        'url' => $item->url,
                        'guid' => $item->guid,
                        'pub_date' => $item->pub_date
                    );

                    // Add content to item_data for thumbnail extraction (same as single refresh)
                    if (!empty($item->content)) {
                        $item_data['content'] = $item->content;
                        error_log("RSS Aggregator: Added content to item_data for post {$post->post_id}");
                    } else {
                        error_log("RSS Aggregator: No content available for post {$post->post_id}");
                    }

                    // Extract new thumbnail
                    error_log("RSS Aggregator: Extracting thumbnail for post {$post->post_id} with URL: {$post->url}");
                    $thumbnail_url = $thumbnail_extractor->extract_thumbnail($item_data);

                    if ($thumbnail_url) {
                        error_log("RSS Aggregator: Found thumbnail URL: {$thumbnail_url}");

                        try {
                            // Remove existing thumbnail (same as single refresh)
                            delete_post_thumbnail($post->post_id);

                            // Download and attach new thumbnail (same as single refresh)
                            $attachment_id = $aggregator->download_and_attach_image($thumbnail_url, $post->post_id);
                            if ($attachment_id) {
                                set_post_thumbnail($post->post_id, $attachment_id);
                                error_log("RSS Aggregator: Successfully set thumbnail {$attachment_id} for post {$post->post_id}");
                            } else {
                                error_log("RSS Aggregator: Failed to download/attach thumbnail for post {$post->post_id}");
                            }
                        } catch (Exception $thumbnail_error) {
                            error_log("RSS Aggregator: Exception setting thumbnail for post {$post->post_id}: " . $thumbnail_error->getMessage());
                        }
                    } else {
                        error_log("RSS Aggregator: No thumbnail found for post {$post->post_id}");
                    }

                    $refreshed_count++;

                } catch (Exception $e) {
                    $errors[] = sprintf(__('Error refreshing post "%s": %s', 'rss-aggregator'), $post->title, $e->getMessage());
                }
            }

            $message = sprintf(__('Refreshed %d posts', 'rss-aggregator'), $refreshed_count);
            if (!empty($errors)) {
                $message .= '. ' . sprintf(__('%d errors occurred', 'rss-aggregator'), count($errors));
            }

            wp_send_json_success(array(
                'message' => $message,
                'refreshed_count' => $refreshed_count,
                'errors_count' => count($errors),
                'errors' => $errors
            ));

        } catch (Exception $e) {
            wp_send_json_error(__('Error refreshing posts: ', 'rss-aggregator') . $e->getMessage());
        }
    }

}
