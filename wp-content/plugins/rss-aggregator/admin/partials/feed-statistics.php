<?php
/**
 * Feed Statistics View
 *
 * @package RSS_Aggregator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$feed_id = intval($_GET['id'] ?? 0);

if (empty($feed_id)) {
    wp_die(__('Invalid feed ID', 'rss-aggregator'));
}

$database = new RSS_Database();
$statistics = $database->get_feed_statistics($feed_id);

if (empty($statistics)) {
    wp_die(__('Feed not found', 'rss-aggregator'));
}

// Get posts for this feed
$posts = $database->get_feed_posts($feed_id, 50);
?>

<div class="wrap">
    <h1><?php echo sprintf(__('Statistics for: %s', 'rss-aggregator'), esc_html($statistics['feed_name'])); ?></h1>
    
    <div class="rss-aggregator-statistics">
        
        <!-- Statistics Cards -->
        <div class="statistics-cards" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;">
            
            <div class="stat-card" style="background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
                <h3 style="margin: 0 0 10px 0; color: #23282d;"><?php _e('Total Items', 'rss-aggregator'); ?></h3>
                <div style="font-size: 24px; font-weight: bold; color: #0073aa;"><?php echo $statistics['total_items']; ?></div>
            </div>
            
            <div class="stat-card" style="background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
                <h3 style="margin: 0 0 10px 0; color: #23282d;"><?php _e('Published Posts', 'rss-aggregator'); ?></h3>
                <div style="font-size: 24px; font-weight: bold; color: #46b450;"><?php echo isset($statistics['posts_count']) ? $statistics['posts_count'] : 0; ?></div>
            </div>

            <div class="stat-card" style="background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
                <h3 style="margin: 0 0 10px 0; color: #23282d;"><?php _e('Success Rate', 'rss-aggregator'); ?></h3>
                <?php $success_rate = isset($statistics['success_rate']) ? $statistics['success_rate'] : 0; ?>
                <div style="font-size: 24px; font-weight: bold; color: <?php echo $success_rate > 80 ? '#46b450' : ($success_rate > 50 ? '#ffb900' : '#dc3232'); ?>">
                    <?php echo $success_rate; ?>%
                </div>
            </div>

            <div class="stat-card" style="background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
                <h3 style="margin: 0 0 10px 0; color: #23282d;"><?php _e('Updates Count', 'rss-aggregator'); ?></h3>
                <div style="font-size: 24px; font-weight: bold; color: #0073aa;"><?php echo isset($statistics['update_count']) ? $statistics['update_count'] : 0; ?></div>
            </div>
            
            <div class="stat-card" style="background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
                <h3 style="margin: 0 0 10px 0; color: #23282d;"><?php _e('Recent Items (24h)', 'rss-aggregator'); ?></h3>
                <div style="font-size: 24px; font-weight: bold; color: #0073aa;"><?php echo $statistics['recent_items']; ?></div>
            </div>
            
            <div class="stat-card" style="background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
                <h3 style="margin: 0 0 10px 0; color: #23282d;"><?php _e('Last Updated', 'rss-aggregator'); ?></h3>
                <div style="font-size: 14px; color: #666;">
                    <?php
                    $last_updated = isset($statistics['last_updated']) ? $statistics['last_updated'] : null;
                    if ($last_updated) {
                        echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($last_updated));
                    } else {
                        _e('Never', 'rss-aggregator');
                    }
                    ?>
                </div>
            </div>
            
        </div>
        
        <!-- Feed Info -->
        <div class="feed-info" style="background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 5px; margin: 20px 0;">
            <h3><?php _e('Feed Information', 'rss-aggregator'); ?></h3>
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Feed URL', 'rss-aggregator'); ?></th>
                    <td><a href="<?php echo esc_url($statistics['feed_url']); ?>" target="_blank"><?php echo esc_html($statistics['feed_url']); ?></a></td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Created', 'rss-aggregator'); ?></th>
                    <td>
                        <?php
                        $created_at = isset($statistics['created_at']) ? $statistics['created_at'] : null;
                        if ($created_at) {
                            echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($created_at));
                        } else {
                            _e('Unknown', 'rss-aggregator');
                        }
                        ?>
                    </td>
                </tr>
            </table>
        </div>
        
        <!-- Posts List -->
        <div class="feed-posts" style="background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 5px; margin: 20px 0;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <h3 style="margin: 0;"><?php _e('Recent Posts', 'rss-aggregator'); ?></h3>
                <?php if (!empty($posts)): ?>
                    <button type="button" class="button button-primary refresh-all-btn"
                            data-feed-id="<?php echo $feed_id; ?>"
                            data-nonce="<?php echo wp_create_nonce('rss_aggregator_admin'); ?>"
                            title="<?php _e('Refresh all posts content and thumbnails', 'rss-aggregator'); ?>">
                        <?php _e('Refresh All Posts', 'rss-aggregator'); ?>
                    </button>
                <?php endif; ?>
            </div>

            <?php if (!empty($posts)): ?>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('Title', 'rss-aggregator'); ?></th>
                            <th><?php _e('Status', 'rss-aggregator'); ?></th>
                            <th><?php _e('Created', 'rss-aggregator'); ?></th>
                            <th><?php _e('Actions', 'rss-aggregator'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($posts as $post): ?>
                            <tr id="post-row-<?php echo $post->id; ?>">
                                <td>
                                    <strong><?php echo esc_html($post->title); ?></strong>
                                    <?php if ($post->url): ?>
                                        <br><a href="<?php echo esc_url($post->url); ?>" target="_blank" style="color: #666; font-size: 12px;">
                                            <?php _e('View Original', 'rss-aggregator'); ?>
                                        </a>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($post->post_id && $post->post_status): ?>
                                        <span class="status-<?php echo $post->post_status; ?>" style="padding: 2px 8px; border-radius: 3px; font-size: 11px; 
                                              background: <?php echo $post->post_status === 'publish' ? '#46b450' : '#ffb900'; ?>; 
                                              color: white;">
                                            <?php echo ucfirst($post->post_status); ?>
                                        </span>
                                        <br><a href="<?php echo get_edit_post_link($post->post_id); ?>" style="font-size: 12px;">
                                            <?php _e('Edit Post', 'rss-aggregator'); ?>
                                        </a>
                                    <?php elseif ($post->processed): ?>
                                        <span style="color: #dc3232;"><?php _e('Post Deleted', 'rss-aggregator'); ?></span>
                                    <?php else: ?>
                                        <span style="color: #666;"><?php _e('Not Processed', 'rss-aggregator'); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($post->created_at)); ?>
                                </td>
                                <td>
                                    <?php if ($post->post_id): ?>
                                        <button type="button" class="button button-small refresh-post-btn"
                                                data-item-id="<?php echo $post->id; ?>"
                                                data-post-id="<?php echo $post->post_id; ?>"
                                                data-nonce="<?php echo wp_create_nonce('rss_aggregator_admin'); ?>"
                                                title="<?php _e('Refresh post content and thumbnail', 'rss-aggregator'); ?>">
                                            <?php _e('Refresh', 'rss-aggregator'); ?>
                                        </button>
                                    <?php endif; ?>
                                    <button type="button" class="button button-small delete-post-btn"
                                            data-item-id="<?php echo $post->id; ?>"
                                            data-nonce="<?php echo wp_create_nonce('rss_aggregator_admin'); ?>">
                                        <?php _e('Delete', 'rss-aggregator'); ?>
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p><?php _e('No posts found for this feed.', 'rss-aggregator'); ?></p>
            <?php endif; ?>
        </div>
        
        <!-- Actions -->
        <div class="feed-actions" style="margin: 20px 0;">
            <a href="<?php echo admin_url('admin.php?page=rss-aggregator&action=edit&id=' . $feed_id); ?>" class="button button-primary">
                <?php _e('Edit Feed', 'rss-aggregator'); ?>
            </a>
            <a href="<?php echo admin_url('admin.php?page=rss-aggregator'); ?>" class="button">
                <?php _e('Back to Feeds', 'rss-aggregator'); ?>
            </a>
        </div>
        
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Refresh post handler
    $('.refresh-post-btn').on('click', function() {
        var $btn = $(this);
        var itemId = $btn.data('item-id');
        var postId = $btn.data('post-id');
        var nonce = $btn.data('nonce');

        $btn.prop('disabled', true).text('<?php _e('Refreshing...', 'rss-aggregator'); ?>');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'rss_aggregator_refresh_post',
                item_id: itemId,
                post_id: postId,
                nonce: nonce
            },
            success: function(response) {
                if (response.success) {
                    alert('<?php _e('Post refreshed successfully!', 'rss-aggregator'); ?>');
                    // Optionally reload the page to show updated data
                    location.reload();
                } else {
                    alert('<?php _e('Error:', 'rss-aggregator'); ?> ' + response.data);
                }
            },
            error: function() {
                alert('<?php _e('AJAX error occurred', 'rss-aggregator'); ?>');
            },
            complete: function() {
                $btn.prop('disabled', false).text('<?php _e('Refresh', 'rss-aggregator'); ?>');
            }
        });
    });

    // Refresh all posts handler
    $('.refresh-all-btn').on('click', function() {
        var $btn = $(this);
        var feedId = $btn.data('feed-id');
        var nonce = $btn.data('nonce');

        if (!confirm('<?php _e('Are you sure you want to refresh all posts for this feed? This may take some time.', 'rss-aggregator'); ?>')) {
            return;
        }

        $btn.prop('disabled', true).text('<?php _e('Refreshing All...', 'rss-aggregator'); ?>');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            timeout: 120000, // 2 minutes timeout
            data: {
                action: 'rss_aggregator_refresh_all_posts',
                feed_id: feedId,
                nonce: nonce
            },
            success: function(response) {
                console.log('AJAX Success Response:', response);
                if (response.success) {
                    alert('<?php _e('All posts refreshed successfully!', 'rss-aggregator'); ?> ' + response.data.message);
                    location.reload();
                } else {
                    alert('<?php _e('Error:', 'rss-aggregator'); ?> ' + response.data);
                }
            },
            error: function(xhr, status, error) {
                console.log('AJAX Error:', xhr, status, error);
                console.log('Response Text:', xhr.responseText);
                if (status === 'timeout') {
                    alert('<?php _e('Request timed out. The operation may still be running in the background.', 'rss-aggregator'); ?>');
                } else {
                    alert('<?php _e('AJAX error occurred:', 'rss-aggregator'); ?> ' + status + ' - ' + error);
                }
            },
            complete: function() {
                $btn.prop('disabled', false).text('<?php _e('Refresh All Posts', 'rss-aggregator'); ?>');
            }
        });
    });

    $('.delete-post-btn').on('click', function() {
        var $btn = $(this);
        var itemId = $btn.data('item-id');
        var nonce = $btn.data('nonce');
        
        if (!confirm('<?php _e('Are you sure you want to delete this post?', 'rss-aggregator'); ?>')) {
            return;
        }
        
        $btn.prop('disabled', true).text('<?php _e('Deleting...', 'rss-aggregator'); ?>');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'rss_aggregator_delete_feed_post',
                item_id: itemId,
                nonce: nonce
            },
            success: function(response) {
                if (response.success) {
                    $('#post-row-' + itemId).fadeOut(function() {
                        $(this).remove();
                    });
                } else {
                    alert('<?php _e('Error:', 'rss-aggregator'); ?> ' + response.data);
                    $btn.prop('disabled', false).text('<?php _e('Delete', 'rss-aggregator'); ?>');
                }
            },
            error: function() {
                alert('<?php _e('An error occurred while deleting the post.', 'rss-aggregator'); ?>');
                $btn.prop('disabled', false).text('<?php _e('Delete', 'rss-aggregator'); ?>');
            }
        });
    });
});
</script>
