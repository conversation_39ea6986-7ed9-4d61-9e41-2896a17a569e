<?php
/**
 * Debug Autocomplete functionality
 * 
 * Access via: /wp-content/plugins/rss-aggregator/debug-autocomplete.php
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. You must be an administrator to run this script.');
}

echo "<h1>RSS Aggregator Autocomplete Debug</h1>";

// Test direct database calls
if (class_exists('RSS_Database')) {
    $database = new RSS_Database();
    
    echo "<h2>Direct Database Tests</h2>";
    
    // Test places
    echo "<h3>Places Search Test</h3>";
    $places = $database->search_geodirectory_places('Foto', 5);
    echo "<p>Search for 'Foto' returned:</p>";
    echo "<pre>" . print_r($places, true) . "</pre>";
    
    // Test users
    echo "<h3>Users Search Test</h3>";
    $users = $database->search_users('admin', 5);
    echo "<p>Search for 'admin' returned:</p>";
    echo "<pre>" . print_r($users, true) . "</pre>";
    
    // Test counties
    echo "<h3>Counties Search Test</h3>";
    $counties = $database->search_counties('dąb', 5);
    echo "<p>Search for 'dąb' returned:</p>";
    echo "<pre>" . print_r($counties, true) . "</pre>";
    
} else {
    echo "<p style='color: red;'>RSS_Database class not found!</p>";
}

// Test AJAX endpoints directly
echo "<h2>AJAX Endpoint Tests</h2>";

// Simulate AJAX calls
$_POST['search'] = 'Foto';
$_POST['nonce'] = wp_create_nonce('rss_aggregator_admin');

if (class_exists('RSS_Admin')) {
    $admin = new RSS_Admin();
    
    echo "<h3>Places AJAX Test</h3>";
    ob_start();
    try {
        $admin->ajax_search_places();
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    }
    $output = ob_get_clean();
    echo "<p>AJAX output: <code>" . htmlspecialchars($output) . "</code></p>";
    
    echo "<h3>Counties AJAX Test</h3>";
    $_POST['search'] = 'dąb';
    ob_start();
    try {
        $admin->ajax_search_counties();
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    }
    $output = ob_get_clean();
    echo "<p>AJAX output: <code>" . htmlspecialchars($output) . "</code></p>";
    
} else {
    echo "<p style='color: red;'>RSS_Admin class not found!</p>";
}

// Test WordPress prefix
global $wpdb;
echo "<h2>WordPress Database Info</h2>";
echo "<p>Table prefix: <strong>{$wpdb->prefix}</strong></p>";
echo "<p>Posts table: <strong>{$wpdb->posts}</strong></p>";
echo "<p>Postmeta table: <strong>{$wpdb->postmeta}</strong></p>";
echo "<p>Users table: <strong>{$wpdb->users}</strong></p>";

// Check if our tables exist
$feeds_table = $wpdb->prefix . 'rss_aggregator_feeds';
$items_table = $wpdb->prefix . 'rss_aggregator_items';

$feeds_exists = $wpdb->get_var("SHOW TABLES LIKE '{$feeds_table}'") == $feeds_table;
$items_exists = $wpdb->get_var("SHOW TABLES LIKE '{$items_table}'") == $items_table;

echo "<p>Feeds table ({$feeds_table}): " . ($feeds_exists ? '✓ EXISTS' : '✗ MISSING') . "</p>";
echo "<p>Items table ({$items_table}): " . ($items_exists ? '✓ EXISTS' : '✗ MISSING') . "</p>";

if ($feeds_exists) {
    $columns = $wpdb->get_results("SHOW COLUMNS FROM {$feeds_table}");
    echo "<h3>Feeds Table Structure:</h3>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li><strong>{$column->Field}</strong> - {$column->Type}</li>";
    }
    echo "</ul>";
}

// Test GeoDirectory
echo "<h2>GeoDirectory Status</h2>";
echo "<p>GeoDirectory class exists: " . (class_exists('GeoDirectory') ? 'YES' : 'NO') . "</p>";
echo "<p>geodir_load_translation function exists: " . (function_exists('geodir_load_translation') ? 'YES' : 'NO') . "</p>";

// Check for GeoDirectory post types
$gd_post_types = array('gd_place', 'geodir_place', 'place', 'listing');
foreach ($gd_post_types as $post_type) {
    $count = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = %s AND post_status = 'publish'",
        $post_type
    ));
    echo "<p>Post type '{$post_type}': {$count} posts</p>";
}

// Test JavaScript variables
echo "<h2>JavaScript Test</h2>";
?>
<div id="js-test-results"></div>
<button id="test-js-vars" type="button">Test JavaScript Variables</button>

<script type="text/javascript">
jQuery(document).ready(function($) {
    $('#test-js-vars').on('click', function() {
        var results = '<h4>JavaScript Variables:</h4>';
        
        if (typeof rssAggregatorAdmin !== 'undefined') {
            results += '<p>✓ rssAggregatorAdmin is defined</p>';
            results += '<p>AJAX URL: ' + rssAggregatorAdmin.ajaxUrl + '</p>';
            results += '<p>Nonce: ' + rssAggregatorAdmin.nonce + '</p>';
        } else {
            results += '<p style="color: red;">✗ rssAggregatorAdmin is NOT defined</p>';
        }
        
        if (typeof jQuery !== 'undefined') {
            results += '<p>✓ jQuery is loaded</p>';
        } else {
            results += '<p style="color: red;">✗ jQuery is NOT loaded</p>';
        }
        
        $('#js-test-results').html(results);
    });
});
</script>

<p><a href="<?php echo admin_url('admin.php?page=rss-aggregator'); ?>">Go to RSS Aggregator</a></p>
