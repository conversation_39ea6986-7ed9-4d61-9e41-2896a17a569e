-- RSS Aggregator - Complete Database Schema
-- Execute these commands in phpMyAdmin or your database management tool
-- Prefix: stg_

-- =====================================================
-- 1. CREATE FEEDS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS `stg_rss_aggregator_feeds` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `url` text NOT NULL,
  `geodirectory_place_id` int(11) DEFAULT NULL,
  `geodirectory_place_name` varchar(255) DEFAULT NULL,
  `assigned_user_id` bigint(20) DEFAULT NULL,
  `assigned_user_name` varchar(255) DEFAULT NULL,
  `county` varchar(255) DEFAULT NULL,
  `update_frequency` varchar(50) DEFAULT 'hourly',
  `initial_import_count` int(11) DEFAULT 10,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_updated` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_update_frequency` (`update_frequency`),
  KEY `idx_geodirectory_place` (`geodirectory_place_id`),
  KEY `idx_assigned_user` (`assigned_user_id`),
  KEY `idx_county` (`county`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 2. CREATE ITEMS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS `stg_rss_aggregator_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `feed_id` int(11) NOT NULL,
  `post_id` bigint(20) DEFAULT NULL,
  `title` text NOT NULL,
  `description` longtext,
  `content` longtext,
  `url` text NOT NULL,
  `guid` varchar(255) NOT NULL,
  `pub_date` datetime DEFAULT NULL,
  `thumbnail_url` text DEFAULT NULL,
  `author` varchar(255) DEFAULT NULL,
  `categories` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_feed_guid` (`feed_id`, `guid`),
  KEY `idx_feed_id` (`feed_id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_pub_date` (`pub_date`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_items_feed` FOREIGN KEY (`feed_id`) REFERENCES `stg_rss_aggregator_feeds` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 3. ADD MISSING COLUMNS TO EXISTING FEEDS TABLE (if needed)
-- =====================================================

-- Check if columns exist and add them if missing
-- These commands will fail silently if columns already exist

ALTER TABLE `stg_rss_aggregator_feeds` 
ADD COLUMN `geodirectory_place_name` varchar(255) DEFAULT NULL AFTER `geodirectory_place_id`;

ALTER TABLE `stg_rss_aggregator_feeds` 
ADD COLUMN `assigned_user_id` bigint(20) DEFAULT NULL AFTER `geodirectory_place_name`;

ALTER TABLE `stg_rss_aggregator_feeds` 
ADD COLUMN `assigned_user_name` varchar(255) DEFAULT NULL AFTER `assigned_user_id`;

ALTER TABLE `stg_rss_aggregator_feeds`
ADD COLUMN `initial_import_count` int(11) DEFAULT 10 AFTER `update_frequency`;

-- Add region column (województwo)
ALTER TABLE `stg_rss_aggregator_feeds`
ADD COLUMN `region` varchar(255) DEFAULT NULL AFTER `county`;

-- =====================================================
-- 4. ADD MISSING INDEXES (if needed)
-- =====================================================

ALTER TABLE `stg_rss_aggregator_feeds` 
ADD INDEX `idx_assigned_user` (`assigned_user_id`);

ALTER TABLE `stg_rss_aggregator_feeds` 
ADD INDEX `idx_geodirectory_place` (`geodirectory_place_id`);

ALTER TABLE `stg_rss_aggregator_feeds` 
ADD INDEX `idx_county` (`county`);

-- =====================================================
-- 5. VERIFY TABLES STRUCTURE
-- =====================================================

-- Run these to check if everything was created correctly:
-- SHOW COLUMNS FROM `stg_rss_aggregator_feeds`;
-- SHOW COLUMNS FROM `stg_rss_aggregator_items`;
-- SHOW INDEX FROM `stg_rss_aggregator_feeds`;
-- SHOW INDEX FROM `stg_rss_aggregator_items`;

-- =====================================================
-- 6. SAMPLE DATA (optional)
-- =====================================================

-- Insert sample feed for testing
-- INSERT INTO `stg_rss_aggregator_feeds` 
-- (`name`, `url`, `county`, `update_frequency`, `initial_import_count`, `status`) 
-- VALUES 
-- ('Test Feed', 'https://example.com/rss', 'Test County', 'hourly', 10, 'active');

-- =====================================================
-- 7. CLEANUP COMMANDS (if you need to start fresh)
-- =====================================================

-- DANGER: These commands will delete all data!
-- Uncomment only if you want to completely reset the tables

-- DROP TABLE IF EXISTS `stg_rss_aggregator_items`;
-- DROP TABLE IF EXISTS `stg_rss_aggregator_feeds`;

-- =====================================================
-- 8. EXPECTED FINAL STRUCTURE
-- =====================================================

/*
stg_rss_aggregator_feeds:
- id (int, auto_increment, primary key)
- name (varchar 255, not null)
- url (text, not null)
- geodirectory_place_id (int, nullable)
- geodirectory_place_name (varchar 255, nullable)
- assigned_user_id (bigint, nullable)
- assigned_user_name (varchar 255, nullable)
- county (varchar 255, nullable)
- update_frequency (varchar 50, default 'hourly')
- initial_import_count (int, default 10)
- status (enum 'active'/'inactive', default 'active')
- created_at (timestamp, default current_timestamp)
- last_updated (timestamp, nullable)

stg_rss_aggregator_items:
- id (int, auto_increment, primary key)
- feed_id (int, not null, foreign key)
- post_id (bigint, nullable)
- title (text, not null)
- description (longtext, nullable)
- content (longtext, nullable)
- url (text, not null)
- guid (varchar 255, not null)
- pub_date (datetime, nullable)
- thumbnail_url (text, nullable)
- author (varchar 255, nullable)
- categories (text, nullable)
- created_at (timestamp, default current_timestamp)
*/
