<?php
/**
 * RSS Fetcher class
 *
 * @package RSS_Aggregator
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * RSS Fetcher class
 */
class RSS_Fetcher {
    
    /**
     * User agent for HTTP requests
     */
    private $user_agent = 'RSS Aggregator WordPress Plugin/1.0.0';
    
    /**
     * Request timeout in seconds
     */
    private $timeout = 30;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Set up WordPress HTTP API defaults
        add_filter('http_request_args', array($this, 'modify_http_request_args'), 10, 2);
    }
    
    /**
     * Fetch RSS feed content
     *
     * @param string $url Feed URL
     * @return string|false Feed content on success, false on failure
     */
    public function fetch_feed($url) {
        if (empty($url) || !filter_var($url, FILTER_VALIDATE_URL)) {
            return false;
        }
        
        // Check cache first
        $cache_key = 'rss_aggregator_feed_' . md5($url);
        $cached_content = get_transient($cache_key);
        
        if ($cached_content !== false) {
            return $cached_content;
        }
        
        // Fetch feed using WordPress HTTP API
        $response = wp_remote_get($url, array(
            'timeout' => $this->timeout,
            'user-agent' => $this->user_agent,
            'headers' => array(
                'Accept' => 'application/rss+xml, application/xml, text/xml',
                'Accept-Encoding' => 'gzip, deflate'
            ),
            'sslverify' => false // For compatibility with some feeds
        ));
        
        if (is_wp_error($response)) {
            error_log('RSS Aggregator: HTTP error for ' . $url . ': ' . $response->get_error_message());
            return false;
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            error_log('RSS Aggregator: HTTP ' . $response_code . ' for ' . $url);
            return false;
        }
        
        $content = wp_remote_retrieve_body($response);
        
        if (empty($content)) {
            error_log('RSS Aggregator: Empty response for ' . $url);
            return false;
        }
        
        // Validate XML content
        if (!$this->is_valid_xml($content)) {
            error_log('RSS Aggregator: Invalid XML for ' . $url);
            return false;
        }
        
        // Cache the content for 15 minutes
        set_transient($cache_key, $content, 15 * MINUTE_IN_SECONDS);
        
        return $content;
    }
    
    /**
     * Test feed URL
     *
     * @param string $url Feed URL
     * @return array Test result
     */
    public function test_feed($url) {
        if (empty($url) || !filter_var($url, FILTER_VALIDATE_URL)) {
            return array(
                'success' => false,
                'message' => __('Invalid URL format', 'rss-aggregator')
            );
        }
        
        // Try to fetch the feed
        $content = $this->fetch_feed($url);
        
        if ($content === false) {
            return array(
                'success' => false,
                'message' => __('Failed to fetch feed content', 'rss-aggregator')
            );
        }
        
        // Try to parse the feed
        $parser = new RSS_Parser();
        $items = $parser->parse_feed($content, (object)array('id' => 0, 'name' => 'Test'));
        
        if (empty($items)) {
            return array(
                'success' => false,
                'message' => __('No items found in feed or invalid RSS format', 'rss-aggregator')
            );
        }
        
        return array(
            'success' => true,
            'message' => sprintf(__('Feed is valid. Found %d items.', 'rss-aggregator'), count($items)),
            'item_count' => count($items),
            'sample_items' => array_slice($items, 0, 3) // Return first 3 items as sample
        );
    }
    
    /**
     * Fetch image from URL
     *
     * @param string $image_url Image URL
     * @return array|false Image data on success, false on failure
     */
    public function fetch_image($image_url) {
        if (empty($image_url) || !filter_var($image_url, FILTER_VALIDATE_URL)) {
            return false;
        }
        
        // Check if it's an image URL
        $image_extensions = array('jpg', 'jpeg', 'png', 'gif', 'webp');
        $url_parts = parse_url($image_url);
        $path_info = pathinfo($url_parts['path']);
        
        if (!isset($path_info['extension']) || !in_array(strtolower($path_info['extension']), $image_extensions)) {
            // Try to detect content type from headers
            $headers = wp_remote_head($image_url, array('timeout' => 10));
            if (!is_wp_error($headers)) {
                $content_type = wp_remote_retrieve_header($headers, 'content-type');
                if (!str_starts_with($content_type, 'image/')) {
                    return false;
                }
            }
        }
        
        // Fetch image
        $response = wp_remote_get($image_url, array(
            'timeout' => 20,
            'user-agent' => $this->user_agent
        ));
        
        if (is_wp_error($response)) {
            return false;
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            return false;
        }
        
        $image_data = wp_remote_retrieve_body($response);
        $content_type = wp_remote_retrieve_header($response, 'content-type');
        
        if (empty($image_data) || !str_starts_with($content_type, 'image/')) {
            return false;
        }
        
        return array(
            'data' => $image_data,
            'content_type' => $content_type,
            'size' => strlen($image_data)
        );
    }
    
    /**
     * Fetch page content to extract thumbnail
     *
     * @param string $page_url Page URL
     * @return string|false Page HTML content on success, false on failure
     */
    public function fetch_page_content($page_url) {
        if (empty($page_url) || !filter_var($page_url, FILTER_VALIDATE_URL)) {
            return false;
        }
        
        // Check cache first
        $cache_key = 'rss_aggregator_page_' . md5($page_url);
        $cached_content = get_transient($cache_key);
        
        if ($cached_content !== false) {
            return $cached_content;
        }
        
        $response = wp_remote_get($page_url, array(
            'timeout' => 20,
            'user-agent' => $this->user_agent,
            'headers' => array(
                'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language' => 'pl,en;q=0.5',
                'Accept-Encoding' => 'gzip, deflate'
            )
        ));
        
        if (is_wp_error($response)) {
            return false;
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            return false;
        }
        
        $content = wp_remote_retrieve_body($response);
        
        if (empty($content)) {
            return false;
        }
        
        // Cache for 1 hour
        set_transient($cache_key, $content, HOUR_IN_SECONDS);
        
        return $content;
    }
    
    /**
     * Check if content is valid XML
     *
     * @param string $content XML content
     * @return bool Is valid XML
     */
    private function is_valid_xml($content) {
        if (empty($content)) {
            return false;
        }
        
        // Disable libxml errors
        $use_errors = libxml_use_internal_errors(true);
        
        // Try to parse XML
        $xml = simplexml_load_string($content);
        
        // Get errors
        $errors = libxml_get_errors();
        
        // Restore error handling
        libxml_use_internal_errors($use_errors);
        
        // Clear errors
        libxml_clear_errors();
        
        return $xml !== false && empty($errors);
    }
    
    /**
     * Modify HTTP request arguments
     *
     * @param array $args Request arguments
     * @param string $url Request URL
     * @return array Modified arguments
     */
    public function modify_http_request_args($args, $url) {
        // Only modify requests from this plugin
        if (strpos($url, 'rss') !== false || strpos($url, 'feed') !== false) {
            // Set reasonable timeout
            if (!isset($args['timeout'])) {
                $args['timeout'] = $this->timeout;
            }
            
            // Set user agent if not set
            if (!isset($args['user-agent'])) {
                $args['user-agent'] = $this->user_agent;
            }
            
            // Follow redirects
            $args['redirection'] = 5;
            
            // Compress response
            $args['compress'] = true;
        }
        
        return $args;
    }
    
    /**
     * Get feed info without full parsing
     *
     * @param string $url Feed URL
     * @return array|false Feed info on success, false on failure
     */
    public function get_feed_info($url) {
        $content = $this->fetch_feed($url);
        
        if ($content === false) {
            return false;
        }
        
        // Parse basic feed info
        $xml = simplexml_load_string($content);
        
        if ($xml === false) {
            return false;
        }
        
        $info = array(
            'title' => '',
            'description' => '',
            'link' => '',
            'language' => '',
            'last_build_date' => '',
            'item_count' => 0
        );
        
        // RSS 2.0
        if (isset($xml->channel)) {
            $channel = $xml->channel;
            $info['title'] = (string)$channel->title;
            $info['description'] = (string)$channel->description;
            $info['link'] = (string)$channel->link;
            $info['language'] = (string)$channel->language;
            $info['last_build_date'] = (string)$channel->lastBuildDate;
            $info['item_count'] = count($channel->item);
        }
        // Atom
        elseif ($xml->getName() === 'feed') {
            $info['title'] = (string)$xml->title;
            $info['description'] = (string)$xml->subtitle;
            $info['link'] = (string)$xml->link['href'];
            $info['language'] = (string)$xml->attributes('xml', true)->lang;
            $info['last_build_date'] = (string)$xml->updated;
            $info['item_count'] = count($xml->entry);
        }
        // RDF
        elseif (isset($xml->channel)) {
            $channel = $xml->channel;
            $info['title'] = (string)$channel->title;
            $info['description'] = (string)$channel->description;
            $info['link'] = (string)$channel->link;
            $info['item_count'] = count($xml->item);
        }
        
        return $info;
    }
    
    /**
     * Check if URL is reachable
     *
     * @param string $url URL to check
     * @return bool Is reachable
     */
    public function is_url_reachable($url) {
        $response = wp_remote_head($url, array(
            'timeout' => 10,
            'user-agent' => $this->user_agent
        ));
        
        if (is_wp_error($response)) {
            return false;
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        return $response_code >= 200 && $response_code < 400;
    }
    
    /**
     * Get final URL after redirects
     *
     * @param string $url Original URL
     * @return string Final URL
     */
    public function get_final_url($url) {
        $response = wp_remote_head($url, array(
            'timeout' => 10,
            'user-agent' => $this->user_agent,
            'redirection' => 5
        ));
        
        if (is_wp_error($response)) {
            return $url;
        }
        
        $final_url = wp_remote_retrieve_header($response, 'location');
        return !empty($final_url) ? $final_url : $url;
    }
}
