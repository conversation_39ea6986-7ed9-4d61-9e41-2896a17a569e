-- Fix RSS Aggregator Items Table
-- Execute in phpMyAdmin
-- Replace 'stg_' with your actual WordPress table prefix

-- Add missing 'processed' column to items table
ALTER TABLE `stg_rss_aggregator_items` 
ADD COLUMN `processed` tinyint(1) DEFAULT 0 AFTER `thumbnail_url`;

-- Add missing 'author' column if it doesn't exist
ALTER TABLE `stg_rss_aggregator_items` 
ADD COLUMN `author` varchar(255) DEFAULT NULL AFTER `processed`;

-- Add missing 'categories' column if it doesn't exist
ALTER TABLE `stg_rss_aggregator_items` 
ADD COLUMN `categories` text DEFAULT NULL AFTER `author`;

-- Add missing 'content' column if it doesn't exist
ALTER TABLE `stg_rss_aggregator_items` 
ADD COLUMN `content` longtext DEFAULT NULL AFTER `description`;

-- Add index for processed column
ALTER TABLE `stg_rss_aggregator_items` 
ADD INDEX `idx_processed` (`processed`);

-- Verify the final structure
SHOW COLUMNS FROM `stg_rss_aggregator_items`;

-- Expected columns:
-- id, feed_id, post_id, title, description, content, url, guid, 
-- pub_date, thumbnail_url, processed, author, categories, created_at

-- Update existing records to mark them as processed
UPDATE `stg_rss_aggregator_items` SET `processed` = 1 WHERE `post_id` IS NOT NULL;

-- Check statistics
SELECT 
    COUNT(*) as total_items,
    COUNT(CASE WHEN processed = 1 THEN 1 END) as processed_items,
    COUNT(CASE WHEN post_id IS NOT NULL THEN 1 END) as items_with_posts
FROM `stg_rss_aggregator_items`;
